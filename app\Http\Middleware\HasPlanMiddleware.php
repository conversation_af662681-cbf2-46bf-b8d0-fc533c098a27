<?php

namespace App\Http\Middleware;

use App\Models\Earning;
use Modules\Plan\Entities\Plan;
use App\Models\UserPlan;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HasPlanMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // check previous url
        $url = str_replace(url('/'), '', url()->previous());
        $path_key = parse_url($url);
        $path = $path_key['path'] ?? '';

        if (auth('user')->check() && authUser() && authUser()->role == 'company') {

            if ($path == '/register') {  // from register page, redirect for account completion first
                return redirect()->route('company.account-progress');
            }

            $user = Auth::user();
            $company = $user->company;
            $plan = $company->userPlan;

            // If company doesn't have a plan, assign the default plan
            if (! $plan) {
                $this->assignDefaultPlan($company); // Assign default plan if not present

                // Check for any pending orders
                $check_pending_plan = $this->checkPendingPlan($company);

                $have_any_session = session()->get('success');
                if ($have_any_session) {
                    flashSuccess($have_any_session);
                } elseif ($check_pending_plan) {
                    flashWarning(__('your_purchased_plan_order_has_pending._please_wait_until_the_order_is_approved'));
                } else {
                    flashWarning(__('a_default_plan_has_been_assigned_to_your_company'));
                }
            }

            return $next($request);
        }

        return $next($request);
    }

    /**
     * Assign the default plan to the company
     *
     * @param  object  $company
     * @return void
     */
    private function assignDefaultPlan(object $company): void
    {
        // Get the default or first available plan
        $plan = Plan::findOrFail(1);

        // Create a new UserPlan if not already assigned
        if (!$company->userPlan) {
            $userPlan = new UserPlan();
            $userPlan->company_id = $company->id;
            $userPlan->plan_id = $plan->id;
            $userPlan->job_limit = 1000; // Memberikan 1000 job posting
            $userPlan->featured_job_limit = $plan->featured_job_limit;
            $userPlan->highlight_job_limit = $plan->highlight_job_limit;
            $userPlan->candidate_cv_view_limit = $plan->candidate_cv_view_limit;
            $userPlan->candidate_cv_view_limitation = 'unlimited'; // Memberikan akses unlimited untuk melihat CV
            $userPlan->save();
        }
    }

    /**
     * Check if the company has any pending plans
     *
     * @param  object  $company
     * @return bool
     */
    public function checkPendingPlan(object $company): bool
    {
        $earnings = Earning::where('company_id', $company->id)
            ->where('payment_status', 'unpaid')
            ->first();

        return (bool) $earnings;
    }
}
