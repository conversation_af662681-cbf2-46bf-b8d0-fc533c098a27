<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\AppliedJob;
use Illuminate\Http\Request;

class CandidateAPIController extends Controller
{
    /**
     * Get candidate documents
     */
    public function getDocuments($id)
    {
        $candidate = Candidate::with(['user', 'resumes'])->findOrFail($id);

        // Cek apakah CV ada dan file tersebut ada di server
        $cvPath = null;
        $resume = $candidate->resumes()->first();
        if ($resume && $resume->file) {
            $cvFullPath = public_path($resume->file);
            if (file_exists($cvFullPath)) {
                $cvPath = asset($resume->file);
            }
        }

        // Log untuk debugging
        \Log::info('Dokumen Pencaker API', [
            'candidate_id' => $id,
            'resume' => $resume ? $resume->file : 'Tidak ada',
            'ak1' => $candidate->user->ak1 ?? 'Tidak ada'
        ]);

        // Cek apakah AK1 ada dan file tersebut ada di server
        $ak1Path = null;
        if ($candidate->user && $candidate->user->ak1) {
            $ak1FullPath = storage_path('app/public/' . $candidate->user->ak1);
            if (file_exists($ak1FullPath)) {
                $ak1Path = asset('public/storage/' . $candidate->user->ak1);
            }
        }

        $data = [
            'cv' => $cvPath,
            'ak1' => $ak1Path,
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get candidate profile
     */
    public function getProfile($id)
    {
        $candidate = Candidate::with(['user', 'education', 'profession', 'skills.translations' => function($query) {
            $query->where('locale', app()->getLocale());
        }])->findOrFail($id);

        // Mengambil nama skill dari relasi skills
        $skillNames = [];
        if ($candidate->skills && $candidate->skills->count() > 0) {
            foreach ($candidate->skills as $skill) {
                // Ambil nama skill dari translations jika ada, jika tidak gunakan nama default
                $translation = $skill->translations->first();
                $skillNames[] = $translation ? $translation->name : $skill->name;
            }
        }

        // Bersihkan HTML tags dari bio
        $bio = $candidate->bio ? strip_tags($candidate->bio) : null;

        $data = [
            'id' => $candidate->id,
            'name' => $candidate->user->name,
            'email' => $candidate->user->email,
            'phone' => $candidate->user->no_hp,
            'nik' => $candidate->user->nik,
            'photo' => $candidate->photo,
            'profession' => $candidate->profession ? $candidate->profession->name : null,
            'gender' => $candidate->gender == 'male' ? 'Laki-laki' : 'Perempuan',
            'birth_date' => $candidate->birth_date ? formatTanggalIndonesia($candidate->birth_date) : null,
            'address' => $candidate->address,
            'education' => $candidate->education ? $candidate->education->name : null,
            'bio' => $bio,
            'skills' => $skillNames,
            'detail_url' => route('candidate.show', $candidate->id),
            'edit_url' => route('candidate.edit', $candidate->id)
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get applied jobs
     */
    public function getAppliedJobs($id)
    {
        // Periksa model AppliedJob yang benar
        $modelClass = class_exists('App\Models\AppliedJob') ? 'App\Models\AppliedJob' : 'App\Models\JobApplication';

        // Gunakan model yang benar
        if ($modelClass == 'App\Models\AppliedJob') {
            $appliedJobs = AppliedJob::with(['job', 'job.company'])
                ->where('candidate_id', $id)
                ->latest()
                ->get();

            $data = $appliedJobs->map(function($item) {
                return [
                    'id' => $item->id,
                    'job_title' => $item->job->title ?? 'Tidak tersedia',
                    'company_name' => $item->job->company->name ?? 'Tidak tersedia',
                    'applied_date' => formatTanggalIndonesia($item->created_at),
                    'status' => $this->getStatusLabel($item->status),
                    'status_class' => $this->getStatusClass($item->status),
                ];
            });
        } else {
            // Jika menggunakan model JobApplication
            $appliedJobs = \App\Models\JobApplication::with(['job', 'job.company'])
                ->where('candidate_id', $id)
                ->latest()
                ->get();

            $data = $appliedJobs->map(function($item) {
                return [
                    'id' => $item->id,
                    'job_title' => $item->job->title ?? 'Tidak tersedia',
                    'company_name' => $item->job->company->name ?? 'Tidak tersedia',
                    'applied_date' => formatTanggalIndonesia($item->created_at),
                    'status' => $this->getStatusLabel($item->status),
                    'status_class' => $this->getStatusClass($item->status),
                ];
            });
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get status label
     */
    private function getStatusLabel($status)
    {
        switch ($status) {
            case 'applied':
                return 'Dilamar';
            case 'rejected':
                return 'Ditolak';
            case 'interview':
                return 'Interview';
            case 'accepted':
                return 'Diterima';
            default:
                return 'Pending';
        }
    }

    /**
     * Get status class
     */
    private function getStatusClass($status)
    {
        switch ($status) {
            case 'applied':
                return 'primary';
            case 'rejected':
                return 'danger';
            case 'interview':
                return 'warning';
            case 'accepted':
                return 'success';
            default:
                return 'secondary';
        }
    }
}
