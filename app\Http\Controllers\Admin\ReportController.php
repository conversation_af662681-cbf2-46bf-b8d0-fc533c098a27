<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppliedJob;
use App\Models\Candidate;
use App\Models\Company;
use App\Models\Job;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Exports\ReportsExport;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    /**
     * Display the reports dashboard
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        try {
            // Ambil tahun-tahun yang tersedia untuk filter
            $years = $this->getAvailableYears();
            $currentYear = date('Y');

            // Data awal untuk tampilan
            $data = [
                'years' => $years,
                'currentYear' => $currentYear,
            ];

            return view('backend.reports.index', $data);
        } catch (\Exception $e) {
            flashError('<PERSON><PERSON><PERSON><PERSON> kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Get data for user comparison chart (Pencaker vs Perusahaan)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserComparisonData(Request $request)
    {
        try {
            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';

            // Query untuk mendapatkan jumlah pencaker dan perusahaan per bulan
            $candidatesData = $this->getUserCountByMonth($year, 'candidate', $location);
            $companiesData = $this->getUserCountByMonth($year, 'company', $location);

            return response()->json([
                'success' => true,
                'candidates' => $candidatesData,
                'companies' => $companiesData,
                'months' => $this->getMonthNames(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for job postings by month
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJobsByMonthData(Request $request)
    {
        try {
            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';

            // Query untuk mendapatkan jumlah lowongan kerja per bulan
            $jobsData = $this->getJobCountByMonth($year, $location);

            return response()->json([
                'success' => true,
                'jobs' => $jobsData,
                'months' => $this->getMonthNames(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for application status chart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getApplicationStatusData(Request $request)
    {
        try {
            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';
            $monthStart = 1; // Default ke bulan Januari
            $monthEnd = 12; // Default ke bulan Desember

            // Query untuk mendapatkan status lamaran kerja
            $statusData = $this->getApplicationStatusCounts($year, $location, $monthStart, $monthEnd);

            return response()->json([
                'success' => true,
                'statusData' => $statusData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for education distribution chart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEducationDistributionData(Request $request)
    {
        try {
            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';
            $monthStart = 1; // Default ke bulan Januari
            $monthEnd = 12; // Default ke bulan Desember

            // Query untuk mendapatkan distribusi pendidikan pencaker
            $educationData = $this->getEducationDistribution($year, $location, $monthStart, $monthEnd);

            return response()->json([
                'success' => true,
                'educationData' => $educationData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for age distribution chart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAgeDistributionData(Request $request)
    {
        try {
            \Log::info('Age Distribution Chart Request', $request->all());

            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';
            $monthStart = 1; // Default ke bulan Januari
            $monthEnd = 12; // Default ke bulan Desember

            // Query untuk mendapatkan distribusi umur pencaker
            $ageData = $this->getAgeDistribution($year, $location, $monthStart, $monthEnd);
            \Log::info('Age Data', ['data' => $ageData]);

            // Pastikan format data konsisten
            $formattedAgeData = [];
            foreach ($ageData as $item) {
                $formattedAgeData[] = [
                    'name' => $item['name'] ?? $item['age_group'] ?? 'Undefined',
                    'count' => (int) $item['count']
                ];
            }

            $response = [
                'success' => true,
                'ageData' => $formattedAgeData,
            ];

            \Log::info('Age Distribution Chart Response', $response);

            return response()->json($response);
        } catch (\Exception $e) {
            \Log::error('Error in getAgeDistributionData: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);

            return response()->json([
                'success' => true, // Tetap kembalikan success=true untuk menghindari error di frontend
                'ageData' => [
                    ['name' => '< 20 tahun', 'count' => 0],
                    ['name' => '20-25 tahun', 'count' => 0],
                    ['name' => '26-30 tahun', 'count' => 0],
                    ['name' => '31-40 tahun', 'count' => 0],
                    ['name' => '41-50 tahun', 'count' => 0],
                    ['name' => '> 50 tahun', 'count' => 0],
                ],
                'error_message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for application trends chart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getApplicationTrendsData(Request $request)
    {
        try {
            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';
            $monthStart = 1; // Default ke bulan Januari
            $monthEnd = 12; // Default ke bulan Desember

            // Query untuk mendapatkan tren lamaran kerja per bulan
            $trendsData = $this->getApplicationTrends($year, $location, $monthStart, $monthEnd);

            return response()->json([
                'success' => true,
                'trendsData' => $trendsData,
                'months' => $this->getMonthNames(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for job absorption chart (accepted applications)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJobAbsorptionData(Request $request)
    {
        try {
            \Log::info('Job Absorption Chart Request', $request->all());

            $year = $request->year ?? date('Y');

            // Data untuk Tangerang Selatan
            $tangselData = $this->getAcceptedApplicationsByMonth($year, 'tangsel');
            \Log::info('Tangsel Data', ['data' => $tangselData]);

            // Data untuk Non-Tangerang Selatan
            $nonTangselData = $this->getAcceptedApplicationsByMonth($year, 'non-tangsel');
            \Log::info('Non-Tangsel Data', ['data' => $nonTangselData]);

            $months = $this->getMonthNames();

            $response = [
                'success' => true,
                'tangsel_data' => $tangselData,
                'non_tangsel_data' => $nonTangselData,
                'months' => $months,
            ];

            \Log::info('Job Absorption Chart Response', $response);

            return response()->json($response);
        } catch (\Exception $e) {
            \Log::error('Error in getJobAbsorptionData: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);

            return response()->json([
                'success' => true, // Tetap kembalikan success=true untuk menghindari error di frontend
                'tangsel_data' => array_fill(0, 12, 0),
                'non_tangsel_data' => array_fill(0, 12, 0),
                'months' => $this->getMonthNames(),
                'error_message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get data for job type comparison chart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJobTypeComparisonData(Request $request)
    {
        try {
            \Log::info('Job Type Comparison Chart Request', $request->all());

            $year = $request->year ?? date('Y');
            $location = $request->location ?? 'all';

            // Query untuk mendapatkan distribusi jenis pekerjaan
            $jobTypes = $this->getJobTypeDistribution($year, $location);
            \Log::info('Job Types Data', ['data' => $jobTypes]);

            // Dapatkan data jenis pekerjaan per bulan
            $jobTypesByMonth = $this->getJobTypesByMonth($year, $location);
            \Log::info('Job Types By Month Data', ['data' => $jobTypesByMonth]);

            // Dapatkan data jenis pekerjaan berdasarkan lokasi
            $jobTypesByLocation = $this->getJobTypesByLocation($year);
            \Log::info('Job Types By Location Data', ['data' => $jobTypesByLocation]);

            // Log jika tidak ada data
            if (empty($jobTypes) || array_sum(array_column($jobTypes, 'count')) == 0) {
                \Log::info('No job type data found for the selected period');
            }

            $response = [
                'success' => true,
                'jobTypes' => $jobTypes,
                'jobTypesByMonth' => $jobTypesByMonth,
                'jobTypesByLocation' => $jobTypesByLocation,
                'months' => $this->getMonthNames(),
            ];

            \Log::info('Job Type Comparison Chart Response', $response);

            return response()->json($response);
        } catch (\Exception $e) {
            \Log::error('Error in getJobTypeComparisonData: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);

            return response()->json([
                'success' => true, // Tetap kembalikan success=true untuk menghindari error di frontend
                'jobTypes' => [
                    ['name' => 'Full Time', 'count' => 0, 'job_type' => 'full_time'],
                    ['name' => 'Part Time', 'count' => 0, 'job_type' => 'part_time'],
                    ['name' => 'Kontrak', 'count' => 0, 'job_type' => 'contractual'],
                    ['name' => 'Magang', 'count' => 0, 'job_type' => 'internship'],
                ],
                'jobTypesByMonth' => [],
                'jobTypesByLocation' => [
                    'tangsel' => [],
                    'non_tangsel' => []
                ],
                'months' => $this->getMonthNames(),
                'error_message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Export reports to PDF
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function exportPdf(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'exportData' => 'required|array',
                'years' => 'required|array',
                'month_start' => 'required|integer|min:1|max:12',
                'month_end' => 'required|integer|min:1|max:12',
                'locations' => 'required|array',
            ]);

            // Ambil data dari request
            $selectedReports = $request->exportData;
            $years = $request->years;
            $monthStart = $request->month_start;
            $monthEnd = $request->month_end;
            $locations = $request->locations;

            // Siapkan data untuk setiap jenis laporan yang dipilih
            $reportData = [];

            // Urutkan tahun
            sort($years);

            // Untuk setiap tahun yang dipilih
            foreach ($years as $year) {
                $yearData = [
                    'year' => $year,
                    'reports' => []
                ];

                // Untuk setiap lokasi yang dipilih
                foreach ($locations as $location) {
                    $locationData = [
                        'location' => $this->getLocationName($location),
                        'data' => []
                    ];

                    // Untuk setiap jenis laporan yang dipilih
                    foreach ($selectedReports as $reportType) {
                        switch ($reportType) {
                            case 'user_comparison':
                                $userData = [
                                    'title' => 'Perbandingan Jumlah Pencaker dan Perusahaan',
                                    'candidates' => $this->getUserCountByMonthRange($year, 'candidate', $location, $monthStart, $monthEnd),
                                    'companies' => $this->getUserCountByMonthRange($year, 'company', $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $userData['is_versus'] = true;
                                    $userData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselCandidates = $this->getUserCountByMonthRange($year, 'candidate', 'tangsel', $monthStart, $monthEnd);
                                        $tangselCompanies = $this->getUserCountByMonthRange($year, 'company', 'tangsel', $monthStart, $monthEnd);

                                        $userData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'total_candidates' => array_sum($tangselCandidates),
                                            'total_companies' => array_sum($tangselCompanies),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselCandidates = $this->getUserCountByMonthRange($year, 'candidate', 'non-tangsel', $monthStart, $monthEnd);
                                        $nonTangselCompanies = $this->getUserCountByMonthRange($year, 'company', 'non-tangsel', $monthStart, $monthEnd);

                                        $userData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'total_candidates' => array_sum($nonTangselCandidates),
                                            'total_companies' => array_sum($nonTangselCompanies),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $userData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'total_candidates' => array_sum($userData['candidates']),
                                        'total_companies' => array_sum($userData['companies']),
                                    ];
                                }

                                $locationData['data']['user_comparison'] = $userData;
                                break;
                            case 'jobs_by_month':
                                $jobsData = [
                                    'title' => 'Data Loker per Bulan',
                                    'jobs' => $this->getJobCountByMonthRange($year, $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $jobsData['is_versus'] = true;
                                    $jobsData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselJobs = $this->getJobCountByMonthRange($year, 'tangsel', $monthStart, $monthEnd);

                                        $jobsData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'total_jobs' => array_sum($tangselJobs),
                                            'monthly_avg' => array_sum($tangselJobs) / count($tangselJobs),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselJobs = $this->getJobCountByMonthRange($year, 'non-tangsel', $monthStart, $monthEnd);

                                        $jobsData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'total_jobs' => array_sum($nonTangselJobs),
                                            'monthly_avg' => array_sum($nonTangselJobs) / count($nonTangselJobs),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $jobsData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'total_jobs' => array_sum($jobsData['jobs']),
                                        'monthly_avg' => array_sum($jobsData['jobs']) / count($jobsData['jobs']),
                                    ];
                                }

                                $locationData['data']['jobs_by_month'] = $jobsData;
                                break;
                            case 'application_status':
                                $statusData = [
                                    'title' => 'Status Lamaran Kerja Pencaker',
                                    'statusData' => $this->getApplicationStatusCounts($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $statusData['is_versus'] = true;
                                    $statusData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselStatusData = $this->getApplicationStatusCounts($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselApplications = array_sum(array_column($tangselStatusData, 'count'));

                                        $statusData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'status_data' => $tangselStatusData,
                                            'total_applications' => $totalTangselApplications,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselStatusData = $this->getApplicationStatusCounts($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselApplications = array_sum(array_column($nonTangselStatusData, 'count'));

                                        $statusData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'status_data' => $nonTangselStatusData,
                                            'total_applications' => $totalNonTangselApplications,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllApplications = array_sum(array_column($statusData['statusData'], 'count'));

                                    $statusData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'status_data' => $statusData['statusData'],
                                        'total_applications' => $totalAllApplications,
                                    ];
                                }

                                $locationData['data']['application_status'] = $statusData;
                                break;
                            case 'education_distribution':
                                $educationData = [
                                    'title' => 'Distribusi Pendidikan Pencaker',
                                    'educationData' => $this->getEducationDistribution($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $educationData['is_versus'] = true;
                                    $educationData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselEducationData = $this->getEducationDistribution($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselCandidates = array_sum(array_column($tangselEducationData, 'count'));

                                        $educationData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'education_data' => $tangselEducationData,
                                            'total_candidates' => $totalTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselEducationData = $this->getEducationDistribution($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselCandidates = array_sum(array_column($nonTangselEducationData, 'count'));

                                        $educationData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'education_data' => $nonTangselEducationData,
                                            'total_candidates' => $totalNonTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllCandidates = array_sum(array_column($educationData['educationData'], 'count'));

                                    $educationData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'education_data' => $educationData['educationData'],
                                        'total_candidates' => $totalAllCandidates,
                                    ];
                                }

                                $locationData['data']['education_distribution'] = $educationData;
                                break;
                            case 'age_distribution':
                                $ageData = [
                                    'title' => 'Distribusi Umur Pencaker',
                                    'ageData' => $this->getAgeDistribution($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $ageData['is_versus'] = true;
                                    $ageData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselAgeData = $this->getAgeDistribution($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselCandidates = array_sum(array_column($tangselAgeData, 'count'));

                                        $ageData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'age_data' => $tangselAgeData,
                                            'total_candidates' => $totalTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselAgeData = $this->getAgeDistribution($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselCandidates = array_sum(array_column($nonTangselAgeData, 'count'));

                                        $ageData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'age_data' => $nonTangselAgeData,
                                            'total_candidates' => $totalNonTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllCandidates = array_sum(array_column($ageData['ageData'], 'count'));

                                    $ageData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'age_data' => $ageData['ageData'],
                                        'total_candidates' => $totalAllCandidates,
                                    ];
                                }

                                $locationData['data']['age_distribution'] = $ageData;
                                break;
                            case 'application_trends':
                                $trendsData = [
                                    'title' => 'Tren Lamaran Kerja per Bulan',
                                    'trendsData' => $this->getApplicationTrends($year, $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $trendsData['is_versus'] = true;
                                    $trendsData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselTrendsData = $this->getApplicationTrends($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselApplications = array_sum($tangselTrendsData);

                                        $trendsData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'trends_data' => $tangselTrendsData,
                                            'total_applications' => $totalTangselApplications,
                                            'monthly_avg' => $totalTangselApplications / count($tangselTrendsData),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselTrendsData = $this->getApplicationTrends($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselApplications = array_sum($nonTangselTrendsData);

                                        $trendsData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'trends_data' => $nonTangselTrendsData,
                                            'total_applications' => $totalNonTangselApplications,
                                            'monthly_avg' => $totalNonTangselApplications / count($nonTangselTrendsData),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllApplications = array_sum($trendsData['trendsData']);

                                    $trendsData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'trends_data' => $trendsData['trendsData'],
                                        'total_applications' => $totalAllApplications,
                                        'monthly_avg' => $totalAllApplications / count($trendsData['trendsData']),
                                    ];
                                }

                                $locationData['data']['application_trends'] = $trendsData;
                                break;
                        }
                    }

                    $yearData['reports'][] = $locationData;
                }

                $reportData[] = $yearData;
            }

            // Siapkan data untuk view PDF
            $viewData = [
                'data' => $reportData,
                'period' => count($years) > 1
                    ? 'Tahun ' . implode(', ', $years)
                    : 'Tahun ' . $years[0] . ' (' . $this->getMonthNames()[$monthStart - 1] . ' - ' . $this->getMonthNames()[$monthEnd - 1] . ')',
                'location' => count($locations) > 1
                    ? 'Semua Lokasi Terpilih'
                    : $this->getLocationName($locations[0]),
                'export_date' => Carbon::now()->format('d F Y H:i:s'),
            ];

            // Generate PDF
            $pdf = \PDF::loadView('backend.reports.pdf', $viewData);
            $pdf->setPaper('a4', 'portrait');
            $pdf->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);

            // Nama file PDF
            $filename = 'Laporan_Statistik_' . date('Y-m-d_H-i-s') . '.pdf';

            // Kembalikan PDF sebagai response untuk diunduh
            return $pdf->download($filename);
        } catch (\Exception $e) {
            \Log::error('Error in exportPdf: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengekspor PDF: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Export reports to Excel
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function exportExcel(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'exportData' => 'required|array',
                'years' => 'required|array',
                'month_start' => 'required|integer|min:1|max:12',
                'month_end' => 'required|integer|min:1|max:12',
                'locations' => 'required|array',
            ]);

            // Ambil data dari request
            $selectedReports = $request->exportData;
            $years = $request->years;
            $monthStart = $request->month_start;
            $monthEnd = $request->month_end;
            $locations = $request->locations;

            // Siapkan data untuk setiap jenis laporan yang dipilih
            $reportData = [];

            // Urutkan tahun
            sort($years);

            // Untuk setiap tahun yang dipilih
            foreach ($years as $year) {
                $yearData = [
                    'year' => $year,
                    'reports' => []
                ];

                // Untuk setiap lokasi yang dipilih
                foreach ($locations as $location) {
                    $locationData = [
                        'location' => $this->getLocationName($location),
                        'data' => []
                    ];

                    // Untuk setiap jenis laporan yang dipilih
                    foreach ($selectedReports as $reportType) {
                        switch ($reportType) {
                            case 'user_comparison':
                                $userData = [
                                    'title' => 'Perbandingan Jumlah Pencaker dan Perusahaan',
                                    'candidates' => $this->getUserCountByMonthRange($year, 'candidate', $location, $monthStart, $monthEnd),
                                    'companies' => $this->getUserCountByMonthRange($year, 'company', $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $userData['is_versus'] = true;
                                    $userData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselCandidates = $this->getUserCountByMonthRange($year, 'candidate', 'tangsel', $monthStart, $monthEnd);
                                        $tangselCompanies = $this->getUserCountByMonthRange($year, 'company', 'tangsel', $monthStart, $monthEnd);

                                        $userData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'total_candidates' => array_sum($tangselCandidates),
                                            'total_companies' => array_sum($tangselCompanies),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselCandidates = $this->getUserCountByMonthRange($year, 'candidate', 'non-tangsel', $monthStart, $monthEnd);
                                        $nonTangselCompanies = $this->getUserCountByMonthRange($year, 'company', 'non-tangsel', $monthStart, $monthEnd);

                                        $userData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'total_candidates' => array_sum($nonTangselCandidates),
                                            'total_companies' => array_sum($nonTangselCompanies),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $userData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'total_candidates' => array_sum($userData['candidates']),
                                        'total_companies' => array_sum($userData['companies']),
                                    ];
                                }

                                $locationData['data']['user_comparison'] = $userData;
                                break;
                            case 'jobs_by_month':
                                $jobsData = [
                                    'title' => 'Data Loker per Bulan',
                                    'jobs' => $this->getJobCountByMonthRange($year, $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $jobsData['is_versus'] = true;
                                    $jobsData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselJobs = $this->getJobCountByMonthRange($year, 'tangsel', $monthStart, $monthEnd);

                                        $jobsData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'total_jobs' => array_sum($tangselJobs),
                                            'monthly_avg' => array_sum($tangselJobs) / count($tangselJobs),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselJobs = $this->getJobCountByMonthRange($year, 'non-tangsel', $monthStart, $monthEnd);

                                        $jobsData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'total_jobs' => array_sum($nonTangselJobs),
                                            'monthly_avg' => array_sum($nonTangselJobs) / count($nonTangselJobs),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $jobsData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'total_jobs' => array_sum($jobsData['jobs']),
                                        'monthly_avg' => array_sum($jobsData['jobs']) / count($jobsData['jobs']),
                                    ];
                                }

                                $locationData['data']['jobs_by_month'] = $jobsData;
                                break;
                            case 'application_status':
                                $statusData = [
                                    'title' => 'Status Lamaran Kerja Pencaker',
                                    'statusData' => $this->getApplicationStatusCounts($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $statusData['is_versus'] = true;
                                    $statusData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselStatusData = $this->getApplicationStatusCounts($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselApplications = array_sum(array_column($tangselStatusData, 'count'));

                                        $statusData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'status_data' => $tangselStatusData,
                                            'total_applications' => $totalTangselApplications,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselStatusData = $this->getApplicationStatusCounts($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselApplications = array_sum(array_column($nonTangselStatusData, 'count'));

                                        $statusData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'status_data' => $nonTangselStatusData,
                                            'total_applications' => $totalNonTangselApplications,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllApplications = array_sum(array_column($statusData['statusData'], 'count'));

                                    $statusData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'status_data' => $statusData['statusData'],
                                        'total_applications' => $totalAllApplications,
                                    ];
                                }

                                $locationData['data']['application_status'] = $statusData;
                                break;
                            case 'education_distribution':
                                $educationData = [
                                    'title' => 'Distribusi Pendidikan Pencaker',
                                    'educationData' => $this->getEducationDistribution($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $educationData['is_versus'] = true;
                                    $educationData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselEducationData = $this->getEducationDistribution($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselCandidates = array_sum(array_column($tangselEducationData, 'count'));

                                        $educationData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'education_data' => $tangselEducationData,
                                            'total_candidates' => $totalTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselEducationData = $this->getEducationDistribution($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselCandidates = array_sum(array_column($nonTangselEducationData, 'count'));

                                        $educationData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'education_data' => $nonTangselEducationData,
                                            'total_candidates' => $totalNonTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllCandidates = array_sum(array_column($educationData['educationData'], 'count'));

                                    $educationData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'education_data' => $educationData['educationData'],
                                        'total_candidates' => $totalAllCandidates,
                                    ];
                                }

                                $locationData['data']['education_distribution'] = $educationData;
                                break;
                            case 'age_distribution':
                                $ageData = [
                                    'title' => 'Distribusi Umur Pencaker',
                                    'ageData' => $this->getAgeDistribution($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $ageData['is_versus'] = true;
                                    $ageData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselAgeData = $this->getAgeDistribution($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselCandidates = array_sum(array_column($tangselAgeData, 'count'));

                                        $ageData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'age_data' => $tangselAgeData,
                                            'total_candidates' => $totalTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselAgeData = $this->getAgeDistribution($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselCandidates = array_sum(array_column($nonTangselAgeData, 'count'));

                                        $ageData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'age_data' => $nonTangselAgeData,
                                            'total_candidates' => $totalNonTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllCandidates = array_sum(array_column($ageData['ageData'], 'count'));

                                    $ageData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'age_data' => $ageData['ageData'],
                                        'total_candidates' => $totalAllCandidates,
                                    ];
                                }

                                $locationData['data']['age_distribution'] = $ageData;
                                break;
                            case 'application_trends':
                                $trendsData = [
                                    'title' => 'Tren Lamaran Kerja per Bulan',
                                    'trendsData' => $this->getApplicationTrends($year, $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $trendsData['is_versus'] = true;
                                    $trendsData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselTrendsData = $this->getApplicationTrends($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselApplications = array_sum($tangselTrendsData);

                                        $trendsData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'trends_data' => $tangselTrendsData,
                                            'total_applications' => $totalTangselApplications,
                                            'monthly_avg' => $totalTangselApplications / count($tangselTrendsData),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselTrendsData = $this->getApplicationTrends($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselApplications = array_sum($nonTangselTrendsData);

                                        $trendsData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'trends_data' => $nonTangselTrendsData,
                                            'total_applications' => $totalNonTangselApplications,
                                            'monthly_avg' => $totalNonTangselApplications / count($nonTangselTrendsData),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllApplications = array_sum($trendsData['trendsData']);

                                    $trendsData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'trends_data' => $trendsData['trendsData'],
                                        'total_applications' => $totalAllApplications,
                                        'monthly_avg' => $totalAllApplications / count($trendsData['trendsData']),
                                    ];
                                }

                                $locationData['data']['application_trends'] = $trendsData;
                                break;
                        }
                    }

                    $yearData['reports'][] = $locationData;
                }

                $reportData[] = $yearData;
            }

            // Siapkan data untuk view Excel
            $period = count($years) > 1
                ? 'Tahun ' . implode(', ', $years)
                : 'Tahun ' . $years[0] . ' (' . $this->getMonthNames()[$monthStart - 1] . ' - ' . $this->getMonthNames()[$monthEnd - 1] . ')';
            $location = count($locations) > 1
                ? 'Semua Lokasi Terpilih'
                : $this->getLocationName($locations[0]);

            // Generate Excel
            $filename = 'Laporan_Statistik_' . date('Y-m-d_H-i-s') . '.xlsx';

            return Excel::download(new ReportsExport($reportData, $period, $location), $filename);
        } catch (\Exception $e) {
            \Log::error('Error in exportExcel: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengekspor Excel: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate PDF preview
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    // Metode previewPdf ada di bawah

    /**
     * Display job absorption report page
     *
     * @return \Illuminate\Http\Response
     */
    public function jobAbsorptionIndex()
    {
        try {
            // Ambil tahun-tahun yang tersedia untuk filter
            $years = $this->getAvailableYears();
            $currentYear = date('Y');

            // Data awal untuk tampilan
            $data = [
                'years' => $years,
                'currentYear' => $currentYear,
            ];

            return view('backend.reports.job_absorption', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Display job type report page
     *
     * @return \Illuminate\Http\Response
     */
    public function jobTypeIndex()
    {
        try {
            // Ambil tahun-tahun yang tersedia untuk filter
            $years = $this->getAvailableYears();
            $currentYear = date('Y');

            // Data awal untuk tampilan
            $data = [
                'years' => $years,
                'currentYear' => $currentYear,
            ];

            return view('backend.reports.job_type', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Display education distribution report page
     *
     * @return \Illuminate\Http\Response
     */
    public function educationIndex()
    {
        try {
            // Ambil tahun-tahun yang tersedia untuk filter
            $years = $this->getAvailableYears();
            $currentYear = date('Y');

            // Data awal untuk tampilan
            $data = [
                'years' => $years,
                'currentYear' => $currentYear,
            ];

            return view('backend.reports.education', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Display age distribution report page
     *
     * @return \Illuminate\Http\Response
     */
    public function ageIndex()
    {
        try {
            // Ambil tahun-tahun yang tersedia untuk filter
            $years = $this->getAvailableYears();
            $currentYear = date('Y');

            // Data awal untuk tampilan
            $data = [
                'years' => $years,
                'currentYear' => $currentYear,
            ];

            return view('backend.reports.age', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    public function previewPdf(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'exportData' => 'required|array',
                'years' => 'required|array',
                'month_start' => 'required|integer|min:1|max:12',
                'month_end' => 'required|integer|min:1|max:12',
                'locations' => 'required|array',
            ]);

            // Ambil data dari request
            $selectedReports = $request->exportData;
            $years = $request->years;
            $monthStart = $request->month_start;
            $monthEnd = $request->month_end;
            $locations = $request->locations;

            // Siapkan data untuk setiap jenis laporan yang dipilih
            $reportData = [];

            // Urutkan tahun
            sort($years);

            // Untuk setiap tahun yang dipilih
            foreach ($years as $year) {
                $yearData = [
                    'year' => $year,
                    'reports' => []
                ];

                // Untuk setiap lokasi yang dipilih
                foreach ($locations as $location) {
                    $locationData = [
                        'location' => $this->getLocationName($location),
                        'data' => []
                    ];

                    // Untuk setiap jenis laporan yang dipilih
                    foreach ($selectedReports as $reportType) {
                        switch ($reportType) {
                            case 'user_comparison':
                                $userData = [
                                    'title' => 'Perbandingan Jumlah Pencaker dan Perusahaan',
                                    'candidates' => $this->getUserCountByMonthRange($year, 'candidate', $location, $monthStart, $monthEnd),
                                    'companies' => $this->getUserCountByMonthRange($year, 'company', $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $userData['is_versus'] = true;
                                    $userData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselCandidates = $this->getUserCountByMonthRange($year, 'candidate', 'tangsel', $monthStart, $monthEnd);
                                        $tangselCompanies = $this->getUserCountByMonthRange($year, 'company', 'tangsel', $monthStart, $monthEnd);

                                        $userData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'total_candidates' => array_sum($tangselCandidates),
                                            'total_companies' => array_sum($tangselCompanies),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselCandidates = $this->getUserCountByMonthRange($year, 'candidate', 'non-tangsel', $monthStart, $monthEnd);
                                        $nonTangselCompanies = $this->getUserCountByMonthRange($year, 'company', 'non-tangsel', $monthStart, $monthEnd);

                                        $userData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'total_candidates' => array_sum($nonTangselCandidates),
                                            'total_companies' => array_sum($nonTangselCompanies),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $userData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'total_candidates' => array_sum($userData['candidates']),
                                        'total_companies' => array_sum($userData['companies']),
                                    ];
                                }

                                $locationData['data']['user_comparison'] = $userData;
                                break;
                            case 'jobs_by_month':
                                $jobsData = [
                                    'title' => 'Data Loker per Bulan',
                                    'jobs' => $this->getJobCountByMonthRange($year, $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $jobsData['is_versus'] = true;
                                    $jobsData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselJobs = $this->getJobCountByMonthRange($year, 'tangsel', $monthStart, $monthEnd);

                                        $jobsData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'total_jobs' => array_sum($tangselJobs),
                                            'monthly_avg' => array_sum($tangselJobs) / count($tangselJobs),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselJobs = $this->getJobCountByMonthRange($year, 'non-tangsel', $monthStart, $monthEnd);

                                        $jobsData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'total_jobs' => array_sum($nonTangselJobs),
                                            'monthly_avg' => array_sum($nonTangselJobs) / count($nonTangselJobs),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $jobsData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'total_jobs' => array_sum($jobsData['jobs']),
                                        'monthly_avg' => array_sum($jobsData['jobs']) / count($jobsData['jobs']),
                                    ];
                                }

                                $locationData['data']['jobs_by_month'] = $jobsData;
                                break;
                            case 'application_status':
                                $statusData = [
                                    'title' => 'Status Lamaran Kerja Pencaker',
                                    'statusData' => $this->getApplicationStatusCounts($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $statusData['is_versus'] = true;
                                    $statusData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselStatusData = $this->getApplicationStatusCounts($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselApplications = array_sum(array_column($tangselStatusData, 'count'));

                                        $statusData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'status_data' => $tangselStatusData,
                                            'total_applications' => $totalTangselApplications,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselStatusData = $this->getApplicationStatusCounts($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselApplications = array_sum(array_column($nonTangselStatusData, 'count'));

                                        $statusData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'status_data' => $nonTangselStatusData,
                                            'total_applications' => $totalNonTangselApplications,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllApplications = array_sum(array_column($statusData['statusData'], 'count'));

                                    $statusData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'status_data' => $statusData['statusData'],
                                        'total_applications' => $totalAllApplications,
                                    ];
                                }

                                $locationData['data']['application_status'] = $statusData;
                                break;
                            case 'education_distribution':
                                $educationData = [
                                    'title' => 'Distribusi Pendidikan Pencaker',
                                    'educationData' => $this->getEducationDistribution($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $educationData['is_versus'] = true;
                                    $educationData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselEducationData = $this->getEducationDistribution($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselCandidates = array_sum(array_column($tangselEducationData, 'count'));

                                        $educationData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'education_data' => $tangselEducationData,
                                            'total_candidates' => $totalTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselEducationData = $this->getEducationDistribution($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselCandidates = array_sum(array_column($nonTangselEducationData, 'count'));

                                        $educationData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'education_data' => $nonTangselEducationData,
                                            'total_candidates' => $totalNonTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllCandidates = array_sum(array_column($educationData['educationData'], 'count'));

                                    $educationData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'education_data' => $educationData['educationData'],
                                        'total_candidates' => $totalAllCandidates,
                                    ];
                                }

                                $locationData['data']['education_distribution'] = $educationData;
                                break;
                            case 'age_distribution':
                                $ageData = [
                                    'title' => 'Distribusi Umur Pencaker',
                                    'ageData' => $this->getAgeDistribution($year, $location, $monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $ageData['is_versus'] = true;
                                    $ageData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselAgeData = $this->getAgeDistribution($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselCandidates = array_sum(array_column($tangselAgeData, 'count'));

                                        $ageData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'age_data' => $tangselAgeData,
                                            'total_candidates' => $totalTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselAgeData = $this->getAgeDistribution($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselCandidates = array_sum(array_column($nonTangselAgeData, 'count'));

                                        $ageData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'age_data' => $nonTangselAgeData,
                                            'total_candidates' => $totalNonTangselCandidates,
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllCandidates = array_sum(array_column($ageData['ageData'], 'count'));

                                    $ageData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'age_data' => $ageData['ageData'],
                                        'total_candidates' => $totalAllCandidates,
                                    ];
                                }

                                $locationData['data']['age_distribution'] = $ageData;
                                break;
                            case 'application_trends':
                                $trendsData = [
                                    'title' => 'Tren Lamaran Kerja per Bulan',
                                    'trendsData' => $this->getApplicationTrends($year, $location, $monthStart, $monthEnd),
                                    'months' => $this->getMonthNamesRange($monthStart, $monthEnd),
                                ];

                                // Jika semua lokasi dipilih dan ada lokasi spesifik lainnya, tambahkan data versus
                                if (in_array('all', $locations) && count($locations) > 1) {
                                    $trendsData['is_versus'] = true;
                                    $trendsData['versus_data'] = [];

                                    // Data untuk Tangerang Selatan
                                    if (in_array('tangsel', $locations)) {
                                        $tangselTrendsData = $this->getApplicationTrends($year, 'tangsel', $monthStart, $monthEnd);
                                        $totalTangselApplications = array_sum($tangselTrendsData);

                                        $trendsData['versus_data'][] = [
                                            'location' => 'Tangerang Selatan',
                                            'trends_data' => $tangselTrendsData,
                                            'total_applications' => $totalTangselApplications,
                                            'monthly_avg' => $totalTangselApplications / count($tangselTrendsData),
                                        ];
                                    }

                                    // Data untuk Non-Tangerang Selatan
                                    if (in_array('non-tangsel', $locations)) {
                                        $nonTangselTrendsData = $this->getApplicationTrends($year, 'non-tangsel', $monthStart, $monthEnd);
                                        $totalNonTangselApplications = array_sum($nonTangselTrendsData);

                                        $trendsData['versus_data'][] = [
                                            'location' => 'Non-Tangerang Selatan',
                                            'trends_data' => $nonTangselTrendsData,
                                            'total_applications' => $totalNonTangselApplications,
                                            'monthly_avg' => $totalNonTangselApplications / count($nonTangselTrendsData),
                                        ];
                                    }

                                    // Data untuk Semua Lokasi
                                    $totalAllApplications = array_sum($trendsData['trendsData']);

                                    $trendsData['versus_data'][] = [
                                        'location' => 'Semua Lokasi',
                                        'trends_data' => $trendsData['trendsData'],
                                        'total_applications' => $totalAllApplications,
                                        'monthly_avg' => $totalAllApplications / count($trendsData['trendsData']),
                                    ];
                                }

                                $locationData['data']['application_trends'] = $trendsData;
                                break;
                        }
                    }

                    $yearData['reports'][] = $locationData;
                }

                $reportData[] = $yearData;
            }

            // Siapkan data untuk view PDF
            $viewData = [
                'data' => $reportData,
                'period' => count($years) > 1
                    ? 'Tahun ' . implode(', ', $years)
                    : 'Tahun ' . $years[0] . ' (' . $this->getMonthNames()[$monthStart - 1] . ' - ' . $this->getMonthNames()[$monthEnd - 1] . ')',
                'location' => count($locations) > 1
                    ? 'Semua Lokasi Terpilih'
                    : $this->getLocationName($locations[0]),
                'export_date' => Carbon::now()->format('d F Y H:i:s'),
            ];

            // Generate PDF
            $pdf = \PDF::loadView('backend.reports.pdf', $viewData);
            $pdf->setPaper('a4', 'portrait');
            $pdf->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);

            // Tampilkan PDF di browser
            return $pdf->stream('preview.pdf');
        } catch (\Exception $e) {
            \Log::error('Error in previewPdf: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat preview PDF: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get location name from location code
     *
     * @param string $locationCode
     * @return string
     */
    private function getLocationName($locationCode)
    {
        switch ($locationCode) {
            case 'all':
                return 'Semua Lokasi';
            case 'tangsel':
                return 'Tangerang Selatan';
            case 'non-tangsel':
                return 'Non-Tangerang Selatan';
            default:
                return 'Tidak Diketahui';
        }
    }

    /**
     * Get month names for a specific range
     *
     * @param int $start
     * @param int $end
     * @return array
     */
    private function getMonthNamesRange($start, $end)
    {
        $allMonths = $this->getMonthNames();
        $months = [];

        for ($i = $start - 1; $i < $end; $i++) {
            $months[] = $allMonths[$i];
        }

        return $months;
    }

    /**
     * Get user count by month for a specific year, role, and month range
     *
     * @param string $year
     * @param string $role
     * @param string $location
     * @param int $monthStart
     * @param int $monthEnd
     * @return array
     */
    private function getUserCountByMonthRange($year, $role, $location, $monthStart, $monthEnd)
    {
        $query = User::where('role', $role)
            ->whereYear('created_at', $year)
            ->whereRaw('MONTH(created_at) >= ?', [$monthStart])
            ->whereRaw('MONTH(created_at) <= ?', [$monthEnd]);

        // Filter berdasarkan lokasi jika diperlukan
        if ($location !== 'all') {
            if ($role === 'candidate') {
                $query->whereHas('candidate', function ($q) use ($location) {
                    if ($location === 'tangsel') {
                        $q->where('district', 'like', '%Tangerang Selatan%')
                            ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    } else {
                        $q->where('district', 'not like', '%Tangerang Selatan%')
                            ->where(function ($q2) {
                                $q2->where('locality', 'not like', '%Tangerang Selatan%')
                                    ->orWhereNull('locality');
                            });
                    }
                });
            } else {
                $query->whereHas('company', function ($q) use ($location) {
                    if ($location === 'tangsel') {
                        $q->where('district', 'like', '%Tangerang Selatan%')
                            ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    } else {
                        $q->where('district', 'not like', '%Tangerang Selatan%')
                            ->where(function ($q2) {
                                $q2->where('locality', 'not like', '%Tangerang Selatan%')
                                    ->orWhereNull('locality');
                            });
                    }
                });
            }
        }

        $data = $query->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Isi data untuk rentang bulan yang dipilih
        $result = [];
        for ($i = $monthStart; $i <= $monthEnd; $i++) {
            $result[] = $data[$i] ?? 0;
        }

        return $result;
    }

    /**
     * Get job count by month for a specific year and month range
     *
     * @param string $year
     * @param string $location
     * @param int $monthStart
     * @param int $monthEnd
     * @return array
     */
    private function getJobCountByMonthRange($year, $location, $monthStart, $monthEnd)
    {
        $query = Job::whereYear('created_at', $year)
            ->whereRaw('MONTH(created_at) >= ?', [$monthStart])
            ->whereRaw('MONTH(created_at) <= ?', [$monthEnd]);

        // Filter berdasarkan lokasi jika diperlukan
        if ($location !== 'all') {
            if ($location === 'tangsel') {
                $query->where(function ($q) {
                    $q->where('district', 'like', '%Tangerang Selatan%')
                        ->orWhere('locality', 'like', '%Tangerang Selatan%');
                });
            } else {
                $query->where('district', 'not like', '%Tangerang Selatan%')
                    ->where(function ($q) {
                        $q->where('locality', 'not like', '%Tangerang Selatan%')
                            ->orWhereNull('locality');
                    });
            }
        }

        $data = $query->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Isi data untuk rentang bulan yang dipilih
        $result = [];
        for ($i = $monthStart; $i <= $monthEnd; $i++) {
            $result[] = $data[$i] ?? 0;
        }

        return $result;
    }

    /**
     * Get application status counts for a specific year and month range
     *
     * @param string $year
     * @param string $location
     * @param int $monthStart
     * @param int $monthEnd
     * @return array
     */
    private function getApplicationStatusCounts($year, $location, $monthStart, $monthEnd)
    {
        try {
            // Periksa apakah tabel application_groups ada
            if (!Schema::hasTable('application_groups')) {
                \Log::error('Table application_groups does not exist');
                return $this->getDefaultApplicationStatusData();
            }

            // Periksa apakah kolom application_group_id ada di tabel applied_jobs
            if (!Schema::hasColumn('applied_jobs', 'application_group_id')) {
                \Log::error('Column application_group_id does not exist in applied_jobs table');
                return $this->getDefaultApplicationStatusData();
            }

            $query = DB::table('applied_jobs')
                ->join('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->whereYear('applied_jobs.created_at', $year)
                ->whereRaw('MONTH(applied_jobs.created_at) >= ?', [$monthStart])
                ->whereRaw('MONTH(applied_jobs.created_at) <= ?', [$monthEnd]);

            // Filter berdasarkan lokasi jika diperlukan
            if ($location !== 'all') {
                $query->join('jobs', 'applied_jobs.job_id', '=', 'jobs.id');

                if ($location === 'tangsel') {
                    $query->where(function ($q) {
                        $q->where('jobs.district', 'like', '%Tangerang Selatan%')
                            ->orWhere('jobs.locality', 'like', '%Tangerang Selatan%');
                    });
                } else {
                    $query->where('jobs.district', 'not like', '%Tangerang Selatan%')
                        ->where(function ($q) {
                            $q->where('jobs.locality', 'not like', '%Tangerang Selatan%')
                                ->orWhereNull('jobs.locality');
                        });
                }
            }

            $data = $query->select('application_groups.name', DB::raw('COUNT(*) as count'))
                ->groupBy('application_groups.name')
                ->get()
                ->toArray();

            // Jika tidak ada data, berikan data default
            if (empty($data)) {
                return $this->getDefaultApplicationStatusData();
            }

            return json_decode(json_encode($data), true);
        } catch (\Exception $e) {
            \Log::error('Error in getApplicationStatusCounts: ' . $e->getMessage());
            return $this->getDefaultApplicationStatusData();
        }
    }

    /**
     * Get default application status data
     *
     * @return array
     */
    private function getDefaultApplicationStatusData()
    {
        return [
            ['name' => 'Semua Lamaran', 'count' => 0],
            ['name' => 'Interview', 'count' => 0],
            ['name' => 'Diterima', 'count' => 0],
            ['name' => 'Ditolak', 'count' => 0],
        ];
    }

    /**
     * Get education distribution for a specific year and month range
     *
     * @param string $year
     * @param string $location
     * @param int $monthStart
     * @param int $monthEnd
     * @return array
     */
    private function getEducationDistribution($year, $location, $monthStart, $monthEnd)
    {
        try {
            $query = DB::table('users')
                ->select('users.pendidikan_terakhir', DB::raw('COUNT(*) as count'))
                ->where('users.role', 'candidate')
                ->whereYear('users.created_at', $year)
                ->whereRaw('MONTH(users.created_at) >= ?', [$monthStart])
                ->whereRaw('MONTH(users.created_at) <= ?', [$monthEnd])
                ->whereNotNull('users.pendidikan_terakhir');

            // Filter berdasarkan lokasi jika diperlukan
            if ($location !== 'all') {
                $query->join('candidates', 'users.id', '=', 'candidates.user_id');

                if ($location === 'tangsel') {
                    $query->where(function ($q) {
                        $q->where('candidates.district', 'like', '%Tangerang Selatan%')
                            ->orWhere('candidates.locality', 'like', '%Tangerang Selatan%');
                    });
                } else {
                    $query->where('candidates.district', 'not like', '%Tangerang Selatan%')
                        ->where(function ($q) {
                            $q->where('candidates.locality', 'not like', '%Tangerang Selatan%')
                                ->orWhereNull('candidates.locality');
                        });
                }
            }

            $data = $query->groupBy('users.pendidikan_terakhir')
                ->get()
                ->toArray();

            // Jika tidak ada data, berikan data default
            if (empty($data)) {
                return [
                    ['pendidikan_terakhir' => 'SD', 'count' => 0],
                    ['pendidikan_terakhir' => 'SMP', 'count' => 0],
                    ['pendidikan_terakhir' => 'SMA', 'count' => 0],
                    ['pendidikan_terakhir' => 'Diploma', 'count' => 0],
                    ['pendidikan_terakhir' => 'Sarjana', 'count' => 0],
                    ['pendidikan_terakhir' => 'Magister', 'count' => 0],
                    ['pendidikan_terakhir' => 'Doktor', 'count' => 0],
                ];
            }

            return json_decode(json_encode($data), true);
        } catch (\Exception $e) {
            \Log::error('Error in getEducationDistribution: ' . $e->getMessage());

            // Return default data in case of error
            return [
                ['pendidikan_terakhir' => 'SD', 'count' => 0],
                ['pendidikan_terakhir' => 'SMP', 'count' => 0],
                ['pendidikan_terakhir' => 'SMA', 'count' => 0],
                ['pendidikan_terakhir' => 'Diploma', 'count' => 0],
                ['pendidikan_terakhir' => 'Sarjana', 'count' => 0],
                ['pendidikan_terakhir' => 'Magister', 'count' => 0],
                ['pendidikan_terakhir' => 'Doktor', 'count' => 0],
            ];
        }
    }

    /**
     * Get age distribution for a specific year and month range
     *
     * @param string $year
     * @param string $location
     * @param int $monthStart
     * @param int $monthEnd
     * @return array
     */
    private function getAgeDistribution($year, $location, $monthStart, $monthEnd)
    {
        try {
            \Log::info("Getting age distribution for year: {$year}, location: {$location}, monthStart: {$monthStart}, monthEnd: {$monthEnd}");

            $query = DB::table('users')
                ->select(
                    DB::raw('
                        CASE
                            WHEN TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) < 20 THEN "< 20 tahun"
                            WHEN TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 20 AND 25 THEN "20-25 tahun"
                            WHEN TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 26 AND 30 THEN "26-30 tahun"
                            WHEN TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 31 AND 40 THEN "31-40 tahun"
                            WHEN TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 41 AND 50 THEN "41-50 tahun"
                            ELSE "> 50 tahun"
                        END AS age_group
                    '),
                    DB::raw('COUNT(*) as count')
                )
                ->where('users.role', 'candidate')
                ->whereYear('users.created_at', $year)
                ->whereRaw('MONTH(users.created_at) >= ?', [$monthStart])
                ->whereRaw('MONTH(users.created_at) <= ?', [$monthEnd])
                ->whereNotNull('users.tanggal_lahir');

            // Filter berdasarkan lokasi jika diperlukan
            if ($location !== 'all') {
                $query->join('candidates', 'users.id', '=', 'candidates.user_id');

                if ($location === 'tangsel') {
                    $query->where(function ($q) {
                        $q->where('candidates.district', 'like', '%Tangerang Selatan%')
                            ->orWhere('candidates.locality', 'like', '%Tangerang Selatan%');
                    });
                } else {
                    $query->where('candidates.district', 'not like', '%Tangerang Selatan%')
                        ->where(function ($q) {
                            $q->where('candidates.locality', 'not like', '%Tangerang Selatan%')
                                ->orWhereNull('candidates.locality');
                        });
                }
            }

            // Dapatkan SQL query untuk debugging
            $sql = $query->toSql();
            $bindings = $query->getBindings();
            \Log::info("SQL Query: {$sql}", ['bindings' => $bindings]);

            $results = $query->groupBy('age_group')
                ->orderBy(DB::raw('MIN(TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()))'))
                ->get();

            \Log::info("Query result count: " . $results->count());

            // Format hasil untuk chart
            $formattedResults = [];
            foreach ($results as $result) {
                $formattedResults[] = [
                    'name' => $result->age_group,
                    'count' => (int) $result->count
                ];
            }

            // Jika tidak ada data, tambahkan data default
            if (empty($formattedResults)) {
                $formattedResults = [
                    ['name' => '< 20 tahun', 'count' => 0],
                    ['name' => '20-25 tahun', 'count' => 0],
                    ['name' => '26-30 tahun', 'count' => 0],
                    ['name' => '31-40 tahun', 'count' => 0],
                    ['name' => '41-50 tahun', 'count' => 0],
                    ['name' => '> 50 tahun', 'count' => 0],
                ];
            }

            // Urutkan berdasarkan kelompok umur
            usort($formattedResults, function($a, $b) {
                $ageOrder = [
                    '< 20 tahun' => 1,
                    '20-25 tahun' => 2,
                    '26-30 tahun' => 3,
                    '31-40 tahun' => 4,
                    '41-50 tahun' => 5,
                    '> 50 tahun' => 6,
                ];

                return $ageOrder[$a['name']] - $ageOrder[$b['name']];
            });

            return $formattedResults;
        } catch (\Exception $e) {
            \Log::error('Error in getAgeDistribution: ' . $e->getMessage());

            // Return default data in case of error
            return [
                ['name' => '< 20 tahun', 'count' => 0],
                ['name' => '20-25 tahun', 'count' => 0],
                ['name' => '26-30 tahun', 'count' => 0],
                ['name' => '31-40 tahun', 'count' => 0],
                ['name' => '41-50 tahun', 'count' => 0],
                ['name' => '> 50 tahun', 'count' => 0],
            ];
        }
    }

    /**
     * Get application trends by month for a specific year and month range
     *
     * @param string $year
     * @param string $location
     * @param int $monthStart
     * @param int $monthEnd
     * @return array
     */
    private function getApplicationTrends($year, $location, $monthStart, $monthEnd)
    {
        try {
            $query = DB::table('applied_jobs')
                ->whereYear('created_at', $year)
                ->whereRaw('MONTH(created_at) >= ?', [$monthStart])
                ->whereRaw('MONTH(created_at) <= ?', [$monthEnd]);

            // Filter berdasarkan lokasi jika diperlukan
            if ($location !== 'all') {
                $query->join('jobs', 'applied_jobs.job_id', '=', 'jobs.id');

                if ($location === 'tangsel') {
                    $query->where(function ($q) {
                        $q->where('jobs.district', 'like', '%Tangerang Selatan%')
                            ->orWhere('jobs.locality', 'like', '%Tangerang Selatan%');
                    });
                } else {
                    $query->where('jobs.district', 'not like', '%Tangerang Selatan%')
                        ->where(function ($q) {
                            $q->where('jobs.locality', 'not like', '%Tangerang Selatan%')
                                ->orWhereNull('jobs.locality');
                        });
                }
            }

            $data = $query->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
                ->groupBy(DB::raw('MONTH(created_at)'))
                ->orderBy('month')
                ->get()
                ->pluck('count', 'month')
                ->toArray();

            // Isi data untuk rentang bulan yang dipilih
            $result = [];
            for ($i = $monthStart; $i <= $monthEnd; $i++) {
                $result[] = $data[$i] ?? 0;
            }

            return $result;
        } catch (\Exception $e) {
            \Log::error('Error in getApplicationTrends: ' . $e->getMessage());

            // Return default data in case of error
            $result = [];
            for ($i = $monthStart; $i <= $monthEnd; $i++) {
                $result[] = 0;
            }
            return $result;
        }
    }

    /**
     * Get available years for filtering
     *
     * @return array
     */
    private function getAvailableYears()
    {
        // Ambil tahun dari tanggal pembuatan user paling awal
        $earliestYear = User::min(DB::raw('YEAR(created_at)'));
        $currentYear = date('Y');

        // Jika tidak ada data, gunakan tahun saat ini
        if (!$earliestYear) {
            $earliestYear = $currentYear;
        }

        $years = [];
        for ($year = $earliestYear; $year <= $currentYear; $year++) {
            $years[] = $year;
        }

        return $years;
    }

    /**
     * Get month names in Indonesian
     *
     * @return array
     */
    private function getMonthNames()
    {
        return [
            'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        ];
    }

    /**
     * Get user count by month for a specific year and role
     *
     * @param string $year
     * @param string $role
     * @param string $location
     * @return array
     */
    private function getUserCountByMonth($year, $role, $location)
    {
        $query = User::where('role', $role)
            ->whereYear('created_at', $year);

        // Filter berdasarkan lokasi jika diperlukan
        if ($location !== 'all') {
            if ($role === 'candidate') {
                $query->whereHas('candidate', function ($q) use ($location) {
                    if ($location === 'tangsel') {
                        $q->where('district', 'like', '%Tangerang Selatan%')
                            ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    } else {
                        $q->where('district', 'not like', '%Tangerang Selatan%')
                            ->where(function ($q2) {
                                $q2->where('locality', 'not like', '%Tangerang Selatan%')
                                    ->orWhereNull('locality');
                            });
                    }
                });
            } else {
                $query->whereHas('company', function ($q) use ($location) {
                    if ($location === 'tangsel') {
                        $q->where('district', 'like', '%Tangerang Selatan%')
                            ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    } else {
                        $q->where('district', 'not like', '%Tangerang Selatan%')
                            ->where(function ($q2) {
                                $q2->where('locality', 'not like', '%Tangerang Selatan%')
                                    ->orWhereNull('locality');
                            });
                    }
                });
            }
        }

        $data = $query->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Isi data untuk semua bulan (1-12)
        $result = [];
        for ($i = 1; $i <= 12; $i++) {
            $result[] = $data[$i] ?? 0;
        }

        return $result;
    }

    /**
     * Get job count by month for a specific year
     *
     * @param string $year
     * @param string $location
     * @return array
     */
    private function getJobCountByMonth($year, $location)
    {
        $query = Job::whereYear('created_at', $year);

        // Filter berdasarkan lokasi jika diperlukan
        if ($location !== 'all') {
            if ($location === 'tangsel') {
                $query->where(function ($q) {
                    $q->where('district', 'like', '%Tangerang Selatan%')
                        ->orWhere('locality', 'like', '%Tangerang Selatan%');
                });
            } else {
                $query->where('district', 'not like', '%Tangerang Selatan%')
                    ->where(function ($q) {
                        $q->where('locality', 'not like', '%Tangerang Selatan%')
                            ->orWhereNull('locality');
                    });
            }
        }

        $data = $query->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Isi data untuk semua bulan (1-12)
        $result = [];
        for ($i = 1; $i <= 12; $i++) {
            $result[] = $data[$i] ?? 0;
        }

        return $result;
    }

    /**
     * Get accepted applications by month for a specific location
     *
     * @param string $year
     * @param string $location
     * @return array
     */
    private function getAcceptedApplicationsByMonth($year, $location)
    {
        try {
            // Log untuk debugging
            \Log::info("Getting accepted applications for year: {$year}, location: {$location}");

            $query = AppliedJob::whereYear('created_at', $year)
                ->where('status', 'accepted'); // Hanya status diterima

            // Filter berdasarkan lokasi
            if ($location === 'tangsel') {
                $query->whereHas('candidate.user', function ($q) {
                    $q->where(function ($q2) {
                        $q2->where('kabupaten_kota', 'like', '%Tangerang Selatan%')
                          ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    });
                });
            } elseif ($location === 'non-tangsel') {
                $query->whereHas('candidate.user', function ($q) {
                    $q->where(function ($q2) {
                        $q2->where(function ($q3) {
                            $q3->where('kabupaten_kota', 'not like', '%Tangerang Selatan%')
                               ->orWhereNull('kabupaten_kota');
                        })
                        ->where(function ($q3) {
                            $q3->where('locality', 'not like', '%Tangerang Selatan%')
                               ->orWhereNull('locality');
                        });
                    });
                });
            }

            // Dapatkan SQL query untuk debugging
            $sql = $query->toSql();
            $bindings = $query->getBindings();
            \Log::info("SQL Query: {$sql}", ['bindings' => $bindings]);

            $data = $query->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
                ->groupBy(DB::raw('MONTH(created_at)'))
                ->orderBy('month')
                ->get();

            \Log::info("Query result count: " . $data->count());
            \Log::info("Query result data: ", $data->toArray());

            $dataArray = $data->pluck('count', 'month')->toArray();

            // Isi data untuk semua bulan (1-12)
            $result = [];
            for ($i = 1; $i <= 12; $i++) {
                $result[] = $dataArray[$i] ?? 0;
            }

            return $result;
        } catch (\Exception $e) {
            \Log::error("Error in getAcceptedApplicationsByMonth: " . $e->getMessage());
            return array_fill(0, 12, 0); // Return array of zeros in case of error
        }
    }

    /**
     * Get job type distribution
     *
     * @param string $year
     * @param string $location
     * @return array
     */
    private function getJobTypeDistribution($year, $location)
    {
        try {
            // Log untuk debugging
            \Log::info("Getting job type distribution for year: {$year}, location: {$location}");

            $query = Job::whereYear('created_at', $year);

            // Filter berdasarkan lokasi
            if ($location === 'tangsel') {
                $query->where(function ($q) {
                    $q->where('district', 'like', '%Tangerang Selatan%')
                      ->orWhere('locality', 'like', '%Tangerang Selatan%');
                });
            } elseif ($location === 'non-tangsel') {
                $query->where(function ($q) {
                    $q->where('district', 'not like', '%Tangerang Selatan%')
                      ->where(function ($q2) {
                          $q2->where('locality', 'not like', '%Tangerang Selatan%')
                             ->orWhereNull('locality');
                      });
                });
            }

            // Dapatkan SQL query untuk debugging
            $sql = $query->toSql();
            $bindings = $query->getBindings();
            \Log::info("SQL Query: {$sql}", ['bindings' => $bindings]);

            // Gunakan job_type untuk mendapatkan jenis pekerjaan
            $results = $query->select('job_type', DB::raw('COUNT(*) as count'))
                ->groupBy('job_type')
                ->get();

            \Log::info("Query result count: " . $results->count());

            // Mapping job_type ke nama yang lebih deskriptif
            $jobTypeNames = [
                'full_time' => 'Full Time',
                'part_time' => 'Part Time',
                'contractual' => 'Kontrak',
                'temporary' => 'Sementara',
                'internship' => 'Magang',
                'remote' => 'Remote',
                'freelance' => 'Freelance',
            ];

            // Format hasil untuk chart
            $formattedResults = [];
            foreach ($results as $result) {
                $jobTypeName = $jobTypeNames[$result->job_type] ?? ucfirst($result->job_type);
                $formattedResults[] = [
                    'name' => $jobTypeName,
                    'count' => (int) $result->count,
                    'job_type' => $result->job_type
                ];
            }

            // Jika tidak ada data, tambahkan data kosong dengan nilai 0
            if (empty($formattedResults)) {
                $formattedResults = [
                    ['name' => 'Full Time', 'count' => 0, 'job_type' => 'full_time'],
                    ['name' => 'Part Time', 'count' => 0, 'job_type' => 'part_time'],
                    ['name' => 'Kontrak', 'count' => 0, 'job_type' => 'contractual'],
                    ['name' => 'Magang', 'count' => 0, 'job_type' => 'internship'],
                ];
            }

            // Urutkan berdasarkan jumlah (dari terbanyak)
            usort($formattedResults, function($a, $b) {
                return $b['count'] - $a['count'];
            });

            return $formattedResults;
        } catch (\Exception $e) {
            \Log::error("Error in getJobTypeDistribution: " . $e->getMessage());

            // Return data dummy jika terjadi error
            return [
                ['name' => 'Full Time', 'count' => 0, 'job_type' => 'full_time'],
                ['name' => 'Part Time', 'count' => 0, 'job_type' => 'part_time'],
                ['name' => 'Kontrak', 'count' => 0, 'job_type' => 'contractual'],
                ['name' => 'Magang', 'count' => 0, 'job_type' => 'internship'],
            ];
        }
    }

    /**
     * Get job types by month
     *
     * @param string $year
     * @param string $location
     * @return array
     */
    private function getJobTypesByMonth($year, $location)
    {
        try {
            \Log::info("Getting job types by month for year: {$year}, location: {$location}");

            // Dapatkan semua jenis pekerjaan yang ada
            $jobTypes = $this->getJobTypeDistribution($year, $location);

            // Inisialisasi array untuk menyimpan data per bulan
            $monthlyData = [];

            // Untuk setiap jenis pekerjaan, dapatkan data per bulan
            foreach ($jobTypes as $jobType) {
                $query = Job::whereYear('created_at', $year)
                    ->where('job_type', $jobType['job_type']);

                // Filter berdasarkan lokasi
                if ($location === 'tangsel') {
                    $query->where(function ($q) {
                        $q->where('district', 'like', '%Tangerang Selatan%')
                          ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    });
                } elseif ($location === 'non-tangsel') {
                    $query->where(function ($q) {
                        $q->where('district', 'not like', '%Tangerang Selatan%')
                          ->where(function ($q2) {
                              $q2->where('locality', 'not like', '%Tangerang Selatan%')
                                 ->orWhereNull('locality');
                          });
                    });
                }

                // Dapatkan jumlah per bulan
                $monthlyResults = $query->select(
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy(DB::raw('MONTH(created_at)'))
                ->get()
                ->keyBy('month')
                ->toArray();

                // Inisialisasi array untuk menyimpan data per bulan untuk jenis pekerjaan ini
                $monthlyCountsForJobType = array_fill(1, 12, 0);

                // Isi data per bulan
                foreach ($monthlyResults as $month => $result) {
                    $monthlyCountsForJobType[$month] = (int) $result['count'];
                }

                // Tambahkan ke array hasil
                $monthlyData[] = [
                    'name' => $jobType['name'],
                    'job_type' => $jobType['job_type'],
                    'data' => array_values($monthlyCountsForJobType) // Konversi ke array numerik
                ];
            }

            return $monthlyData;
        } catch (\Exception $e) {
            \Log::error("Error in getJobTypesByMonth: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get job types by location
     *
     * @param string $year
     * @return array
     */
    private function getJobTypesByLocation($year)
    {
        try {
            \Log::info("Getting job types by location for year: {$year}");

            // Dapatkan semua jenis pekerjaan yang ada
            $allJobTypes = $this->getJobTypeDistribution($year, 'all');

            // Inisialisasi array untuk menyimpan data per lokasi
            $locationData = [
                'tangsel' => [],
                'non_tangsel' => []
            ];

            // Untuk setiap jenis pekerjaan, dapatkan data per lokasi
            foreach ($allJobTypes as $jobType) {
                // Query untuk Tangerang Selatan
                $tangselQuery = Job::whereYear('created_at', $year)
                    ->where('job_type', $jobType['job_type'])
                    ->where(function ($q) {
                        $q->where('district', 'like', '%Tangerang Selatan%')
                          ->orWhere('locality', 'like', '%Tangerang Selatan%');
                    });

                $tangselCount = $tangselQuery->count();

                // Query untuk Non-Tangerang Selatan
                $nonTangselQuery = Job::whereYear('created_at', $year)
                    ->where('job_type', $jobType['job_type'])
                    ->where(function ($q) {
                        $q->where('district', 'not like', '%Tangerang Selatan%')
                          ->where(function ($q2) {
                              $q2->where('locality', 'not like', '%Tangerang Selatan%')
                                 ->orWhereNull('locality');
                          });
                    });

                $nonTangselCount = $nonTangselQuery->count();

                // Tambahkan ke array hasil
                $locationData['tangsel'][] = [
                    'name' => $jobType['name'],
                    'job_type' => $jobType['job_type'],
                    'count' => $tangselCount
                ];

                $locationData['non_tangsel'][] = [
                    'name' => $jobType['name'],
                    'job_type' => $jobType['job_type'],
                    'count' => $nonTangselCount
                ];
            }

            return $locationData;
        } catch (\Exception $e) {
            \Log::error("Error in getJobTypesByLocation: " . $e->getMessage());
            return [
                'tangsel' => [],
                'non_tangsel' => []
            ];
        }
    }


}
