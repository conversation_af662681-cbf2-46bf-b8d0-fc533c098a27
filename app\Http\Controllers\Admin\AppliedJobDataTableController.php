<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppliedJob;
use App\Models\ApplicationGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class AppliedJobDataTableController extends Controller
{
    /**
     * Handle DataTables AJAX request for applied jobs
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        abort_if(!userCan('job.view'), 403);

        // Buat query dengan select kolom yang dibutuhkan dari semua tabel terkait
        $query = AppliedJob::query()
            ->select([
                'applied_jobs.*',
                'jobs.title as job_title',
                'jobs.company_id',
                'companies.user_id as company_user_id',
                'users.name as company_name',
                'application_groups.name as status_name',
                'default_application_groups.name as default_status_name',
                'candidates.id as candidate_id',
                'candidates.photo as candidate_photo',
                'candidate_users.name as candidate_name',
                'candidate_users.username as candidate_username',
                'candidate_resumes.id as resume_id'
            ])
            ->leftJoin('jobs', 'applied_jobs.job_id', '=', 'jobs.id')
            ->leftJoin('companies', 'jobs.company_id', '=', 'companies.id')
            ->leftJoin('users', 'companies.user_id', '=', 'users.id')
            ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
            ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
            ->leftJoin('candidates', 'applied_jobs.candidate_id', '=', 'candidates.id')
            ->leftJoin('users as candidate_users', 'candidates.user_id', '=', 'candidate_users.id')
            ->leftJoin('candidate_resumes', 'applied_jobs.candidate_resume_id', '=', 'candidate_resumes.id');

        // Filter by job if provided
        if ($request->has('job_id') && $request->job_id) {
            $query->where('applied_jobs.job_id', $request->job_id);
        }

        // Filter by company if provided
        if ($request->has('company_id') && $request->company_id) {
            $query->where('jobs.company_id', $request->company_id);
        }

        // Filter by application group/status if provided
        if ($request->has('status') && $request->status) {
            $query->where(function($q) use ($request) {
                $q->where('application_groups.name', $request->status)
                  ->orWhere('default_application_groups.name', $request->status);
            });
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('candidate', function ($row) {
                if (!$row->candidate_name) {
                    return '-';
                }

                // Cek apakah kandidat memiliki foto
                $photoHtml = '';
                if ($row->candidate_photo) {
                    $photoUrl = asset($row->candidate_photo);
                    $photoHtml = '<img src="' . $photoUrl . '" class="rounded-circle" width="40" height="40" style="object-fit: cover;">';
                } else {
                    $photoHtml = '<div class="candidate-photo-placeholder">
                                    <i class="fas fa-user"></i>
                                  </div>';
                }

                return '<div class="d-flex align-items-center">
                    <div class="candidate-photo-container mr-2">
                        ' . $photoHtml . '
                    </div>
                    <div>
                        <h5 class="mb-0">' . $row->candidate_name . '</h5>
                        <small class="text-muted">@' . $row->candidate_username . '</small>
                    </div>
                </div>';
            })
            ->addColumn('job', function ($row) {
                // Check if job exists
                if (!$row->job_id || !$row->job_title) {
                    return '-';
                }

                // Get job title and company name
                $jobTitle = $row->job_title ?? 'Tidak diketahui';
                $companyName = $row->company_name ?? '-';

                return '<div>
                    <h5 class="mb-0">' . $jobTitle . '</h5>
                    <small class="text-muted">' . $companyName . '</small>
                </div>';
            })
            ->addColumn('status', function ($row) {
                // Prioritaskan default_status_name jika ada
                $status = $row->default_status_name ?? $row->status_name ?? 'Tidak diketahui';

                $statusClass = 'secondary';
                if ($status == 'Diterima' || $status == 'Accepted') {
                    $statusClass = 'success';
                } elseif ($status == 'Interview') {
                    $statusClass = 'info';
                } elseif ($status == 'Ditolak' || $status == 'Tolak' || $status == 'Rejected') {
                    $statusClass = 'danger';
                }

                return '<span class="badge badge-' . $statusClass . '">' . $status . '</span>';
            })
            ->addColumn('date', function ($row) {
                return '<div>
                    <span>' . date('d M Y', strtotime($row->created_at)) . '</span><br>
                    <small class="text-muted">' . date('H:i', strtotime($row->created_at)) . '</small>
                </div>';
            })
            ->addColumn('action', function ($row) {
                $actionBtn = '<div class="d-flex">';

                // View profile button
                if ($row->candidate_username) {
                    $actionBtn .= '<button type="button" class="btn btn-sm btn-outline-primary mr-1 view-profile"
                                    data-username="' . $row->candidate_username . '" data-toggle="tooltip" title="Lihat Profil">
                                    <i class="fas fa-user"></i>
                                </button>';
                }

                // View CV button
                if ($row->resume_id) {
                    // Preview CV button
                    $actionBtn .= '<button type="button" class="btn btn-sm btn-outline-primary mr-1 preview-cv"
                                    data-resume-id="' . $row->resume_id . '" data-toggle="tooltip" title="Preview CV">
                                    <i class="fas fa-eye"></i>
                                </button>';

                    // Download CV button
                    $actionBtn .= '<a href="' . route('website.candidate.download.cv', $row->resume_id) . '"
                                    class="btn btn-sm btn-outline-primary mr-1" data-toggle="tooltip" title="Download CV">
                                    <i class="fas fa-download"></i>
                                </a>';
                }

                // Detail button
                $actionBtn .= '<a href="' . route('applied.job.show', $row->id) . '"
                                class="btn btn-sm btn-info" data-toggle="tooltip" title="Detail Lamaran">
                                <i class="fas fa-info-circle"></i>
                            </a>';

                $actionBtn .= '</div>';

                return $actionBtn;
            })
            ->rawColumns(['candidate', 'job', 'status', 'date', 'action'])
            ->make(true);
    }

    /**
     * Get monthly statistics for applied jobs
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMonthlyStats()
    {
        $currentMonth = date('m');
        $currentYear = date('Y');

        // Base query for current month
        $baseQuery = DB::table('applied_jobs')
            ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
            ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
            ->whereMonth('applied_jobs.created_at', $currentMonth)
            ->whereYear('applied_jobs.created_at', $currentYear);

        // Count total applications
        $total = (clone $baseQuery)->count();

        // Count accepted applications
        $accepted = (clone $baseQuery)
            ->where(function($query) {
                $query->where('application_groups.name', 'Diterima')
                      ->orWhere('application_groups.name', 'Accepted')
                      ->orWhere('default_application_groups.name', 'Diterima');
            })
            ->count();

        // Count interview applications
        $interview = (clone $baseQuery)
            ->where(function($query) {
                $query->where('application_groups.name', 'Interview')
                      ->orWhere('default_application_groups.name', 'Interview');
            })
            ->count();

        // Count rejected applications
        $rejected = (clone $baseQuery)
            ->where(function($query) {
                $query->where('application_groups.name', 'Ditolak')
                      ->orWhere('application_groups.name', 'Tolak')
                      ->orWhere('application_groups.name', 'Rejected')
                      ->orWhere('default_application_groups.name', 'Ditolak');
            })
            ->count();

        return response()->json([
            'accepted' => $accepted,
            'interview' => $interview,
            'rejected' => $rejected,
            'total' => $total
        ]);
    }
}
