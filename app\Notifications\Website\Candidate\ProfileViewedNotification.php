<?php

namespace App\Notifications\Website\Candidate;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ProfileViewedNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $company;
    public $candidate;
    public $type;
    public $job;

    public function __construct($company, $candidate, $type = 'profile', $job = null)
    {
        $this->company = $company;
        $this->candidate = $candidate;
        $this->type = $type;
        $this->job = $job;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $companyName = $this->company->company ? ($this->company->company->legal_entity_type . ' ' . $this->company->name) : $this->company->name;
        $jobTitle = $this->job ? $this->job->title : '';

        if ($this->type == 'profile') {
            $subject = 'Profil Anda Dilihat oleh ' . $companyName;
            $message = 'Profil Anda telah dilihat oleh ' . $companyName;
            if ($this->job) {
                $message .= ' untuk loker "' . $jobTitle . '" yang telah Anda lamar.';
            }
        } else {
            $subject = 'CV Anda Dilihat oleh ' . $companyName;
            $message = 'CV Anda telah dilihat oleh ' . $companyName;
            if ($this->job) {
                $message .= ' untuk loker "' . $jobTitle . '" yang telah Anda lamar.';
            }
        }

        return (new MailMessage)
            ->greeting('Yth, '.$this->candidate->name)
            ->subject($subject)
            ->line($message)
            ->line('Ini menunjukkan bahwa perusahaan tertarik dengan profil Anda.')
            ->line('Terima kasih telah menggunakan '.config('app.name').'.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $companyName = $this->company->company ? ($this->company->company->legal_entity_type . ' ' . $this->company->name) : $this->company->name;
        $jobTitle = $this->job ? $this->job->title : '';

        if ($this->type == 'profile') {
            $title = 'Profil Anda dilihat oleh ' . $companyName;
            if ($this->job) {
                $title .= ' untuk loker "' . $jobTitle . '" yang telah Anda lamar.';
            }
        } else {
            $title = 'CV Anda dilihat oleh ' . $companyName;
            if ($this->job) {
                $title .= ' untuk loker "' . $jobTitle . '" yang telah Anda lamar.';
            }
        }

        $data = [
            'title' => $title,
            'url' => route('website.employe.details', $this->company->username),
            'type' => $this->type,
        ];

        // Tambahkan job_id jika ada
        if ($this->job) {
            $data['job_id'] = $this->job->id;
        }

        return $data;
    }
}
