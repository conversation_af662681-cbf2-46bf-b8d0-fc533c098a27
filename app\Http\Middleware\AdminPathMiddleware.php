<?php

namespace App\Http\Middleware;

use App\Models\Setting;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminPathMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the current admin path from settings
        $setting = Setting::first();
        $adminPath = $setting->admin_path ?? 'admin';

        // Get the requested path
        $path = $request->path();

        // Jika path sudah menggunakan custom admin path, langsung lanjutkan
        if (strpos($path, $adminPath) === 0) {
            return $next($request);
        }

        // Hanya redirect jika path dimulai dengan 'admin/' dan bukan custom path
        // Ini mencegah redirect loop
        if ($path === 'admin' || (strpos($path, 'admin/') === 0 && $adminPath !== 'admin')) {
            // Jika admin path telah diubah, redirect ke path baru
            $newPath = str_replace('admin', $adminPath, $path);

            // Cek apakah kita sudah berada di URL target untuk mencegah redirect loop
            if ($path !== $newPath) {
                return redirect($newPath);
            }
        }

        return $next($request);
    }
}
