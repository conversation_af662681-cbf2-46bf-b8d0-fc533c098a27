<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class AdminPathHelper
{
    /**
     * Mendapatkan custom admin path dari setting
     *
     * @return string
     */
    public static function getAdminPath()
    {
        return Cache::remember('admin_path', 3600, function () {
            return Setting::first()->admin_path ?? 'admin';
        });
    }
}
