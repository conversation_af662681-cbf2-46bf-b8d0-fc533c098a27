<?php

namespace App\Http\Controllers\Api;

use App\Models\Tag;
use App\Models\JobRole;
use App\Models\JobType;
use App\Models\Education;
use App\Models\Experience;
use App\Models\Profession;
use App\Models\JobCategory;
use Illuminate\Http\Request;
use F9Web\ApiResponseHelpers;
use App\Http\Controllers\Controller;
use Modules\Location\Entities\Country;
use Modules\Currency\Entities\Currency;
use Modules\Language\Entities\Language;

class AttributeController extends Controller
{
    use ApiResponseHelpers;

    /**
     * Return a list of all countries.
     *
     * @return \Illuminate\Http\Response
     */
    public function countries(){
        $countries = Country::all()->transform(function($country){
            return [
                'id' => $country->id,
                'name' => $country->name,
                'sortname' => $country->sortname,
                'status' => $country->status,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $countries
        ]);
    }

    /**
     * Change the current country.
     *
     * @param int $countryId Country ID
     * @return \Illuminate\Http\Response
     */
    public function changeCountry($countryId)
    {
        try {
            session()->put('selected_country', $countryId);

            return $this->respondWithSuccess([
                'data' => [
                    'message' => 'Country changed successfully',
                    'current_country' => Country::find($countryId)
                ]
            ]);
            return back();
        } catch (\Exception $e) {
            flashError('An error occurred: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Get all currencies.
     *
     * @return \Illuminate\Http\Response
     */
    public function currencies(){
        $currencies = Currency::all()->transform(function($currency){
            return [
                'id' => $currency->id,
                'name' => $currency->name,
                'code' => $currency->code,
                'symbol' => $currency->symbol,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $currencies
        ]);
    }

    /**
     * Change the current session currency to the given currency code.
     *
     * @param string $code The currency code to change to.
     * @return \Illuminate\Http\Response A response indicating success or error.
     */
    public function changeCurrency($code)
    {
        try {
            $currency = Currency::where('code', $code)->first();
            session(['current_currency' => $currency]);
            currencyRateStore();

            return $this->respondWithSuccess([
                'data' => [
                    'message' => 'Currency changed successfully',
                    'current_currency' => $currency
                ]
            ]);
        } catch (\Exception $e) {
            return $this->respondError('An error occurred: '. $e->getMessage());
        }
    }

    /**
     * Retrieves the current language and a list of all available languages.
     *
     * @return \Illuminate\Http\Response A response containing the current language and the list of all languages.
     */
    public function languageList(){
        $current_language = currentLanguage() ? currentLanguage() : Language::where('code', config('zakirsoft.default_language'))->first();

        return $this->respondWithSuccess([
            'data' => [
                'current_language' => $current_language,
                'languages' => Language::all()
            ]
        ]);
    }

    /**
     * Changes the current session language to the given language code.
     *
     * @param string $code The language code to change to.
     * @return \Illuminate\Http\Response A response indicating success or error.
     */
    public function changeLanguage($code){
        $language = Language::where('code', $code)->first();

        if (!$language) {
            return $this->respondError('language_not_found');
        }

        session(['current_lang' => $language]);
        app()->setLocale($language);

        return $this->respondWithSuccess([
            'data' => [
                'message' => 'Language changed successfully',
                'current_language' => $language
            ]
        ]);
    }

    /**
     * Get all job categories.
     *
     * @return \Illuminate\Http\Response A response containing the job categories.
     */
    public function categories(){
        $categories = JobCategory::all()->transform(function($category){
            return [
                'id' => $category->id,
                'name' => $category->name,
                'image' => $category->image_url,
                'is_svg' => str_contains($category->image, '.svg'),
            ];
        });

        return $this->respondWithSuccess([
            'data' => $categories
        ]);
    }

    /**
     * Retrieves all job roles.
     *
     * @return \Illuminate\Http\Response A response containing all job roles.
     */
    public function job_roles(){
        $job_roles = JobRole::all()->transform(function($role){
            return [
                'id' => $role->id,
                'name' => $role->name,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $job_roles
        ]);
    }

    /**
     * Retrieves all experiences.
     *
     * @return \Illuminate\Http\Response A response containing all job experiences.
     */
    public function experiences(){
        $experiences = Experience::all()->transform(function($experience){
            return [
                'id' => $experience->id,
                'name' => $experience->name,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $experiences
        ]);
    }

    /**
     * Retrieves all educations.
     *
     * @return \Illuminate\Http\Response A response containing all educations.
     */
    public function educations(){
        $educations = Education::all()->transform(function($education){
            return [
                'id' => $education->id,
                'name' => $education->name,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $educations
        ]);
    }

    /**
     * Retrieves all job types.
     *
     * @return \Illuminate\Http\Response A response containing all job types.
     */
    public function job_types(){
        $job_types = JobType::all()->transform(function($job_type){
            return [
                'id' => $job_type->id,
                'name' => $job_type->name,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $job_types
        ]);
    }

    /**
     * Retrieves all professions.
     *
     * @return \Illuminate\Http\Response A response containing all job professions.
     */
    public function professions(){
        $professions = Profession::all()->transform(function($profession){
            return [
                'id' => $profession->id,
                'name' => $profession->name,
            ];
        });

        return $this->respondWithSuccess([
            'data' => $professions
        ]);
    }

    /**
     * Get popular tags
     *
     * @return \Illuminate\Http\Response
     */
    public function popular_tags(){
        $tags = Tag::popular()
            ->withCount('tags')
            ->latest('tags_count')
            ->get()
            ->take(10)
            ->transform(function($tag){
                return [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'slug' => $tag->slug,
                    'jobs_count' => $tag->tags_count,
                    'tags_count' => $tag->tags_count,
                ];
            });

        return $this->respondWithSuccess([
            'data' => $tags,
        ]);
    }

    /**
     * Return the current session settings.
     *
     * @return \Illuminate\Http\Response
     */
    public function currentSession(){
        return $this->respondWithSuccess([
            'data' => [
                'current_country' => selected_country(),
                'current_currency' => currentCurrency(),
                'current_language' => currentLanguage() ? currentLanguage() : loadDefaultLanguage(),
            ]
        ]);
    }

    /**
     * Returns all languages with their translations.
     *
     * @return \Illuminate\Http\Response A response containing all languages with their translations.
     */
    public function fetchTranslations(){
        $languages = Language::all();

        return $this->respondWithSuccess([
            'data' => [
                'default_language_code' => config('templatecookie.default_language'),
                'languages' => $languages->map(function($language){
                    return [
                        'name' => $language->name,
                        'code' => $language->code,
                    ];
                }),
                'translations' => $languages->map(function($language){
                    return [
                        'code' => $language->code,
                        'translations' => \File::json(resource_path("lang/$language->code.json")),
                    ];
                })
            ]
        ]);
    }
}
