<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AppliedJob;
use App\Models\Company;
use App\Models\ApplicationGroup;

class AppliedJobController extends Controller
{
    public function index(Request $request)
    {
        $query = AppliedJob::with(['applicationGroup', 'candidate.user', 'job.company.user']);

        // Filter berdasarkan Nama Kandidat
        if ($request->filled('candidate_name')) {
            $query->whereHas('candidate.user', function ($q) use ($request) {
                $q->where('name', 'LIKE', "%{$request->candidate_name}%");
            });
        }

        // Filter berdasarkan Nama Perusahaan
        if ($request->filled('company_id')) {
            $query->whereHas('job.company', function ($q) use ($request) {
                $q->where('id', $request->company_id);
            });
        }

        // Filter berdasarkan Status Lamaran (Application Group)
        if ($request->filled('status')) {
            $query->where('application_group_id', $request->status);
        }

        // Filter berdasarkan Rentang Tanggal
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
        }

        // Ambil data dengan pagination
        $applied_jobs = $query->paginate(10)->withQueryString();

        // Data untuk dropdown
        $companies = Company::with('user:id,name')->get(['id', 'user_id']);
        $application_groups = ApplicationGroup::all();

        return view('backend.Job.applied_index', compact('applied_jobs', 'companies', 'application_groups'));
    }

    public function show(AppliedJob $applied_job)
    {
        return view('backend.Job.applied_job_show', compact('applied_job'));
    }
}
