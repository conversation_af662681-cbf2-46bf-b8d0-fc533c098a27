<?php

namespace App\Notifications\Website\Candidate;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplyJobNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;

    public $company;

    public $job;

    public function __construct($user, $company, $job)
    {
        $this->user = $user;
        $this->company = $company;
        $this->job = $job;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        // Send both database and mail notifications to all users
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if ($notifiable->role == 'candidate') {
            // Email content for candidates
            return (new MailMessage)
                ->greeting('Yth, '.$this->user->name)
                ->subject("<PERSON><PERSON> untuk <PERSON> {$this->job->title} di {$this->company->name}'")
                ->line("Terima kasih telah melamar pekerjaan untuk posisi {$this->job->title} di {$this->company->name}. Kami telah menerima lamaran kerja Anda dan saat ini sedang ditinjau.")
                ->line("Jika kualifikasi Anda sesuai dengan permintaan, kami akan menghubungi Anda kembali untuk langkah selanjutnya. Terima kasih telah mempertimbangkan {$this->company->name} sebagai calon perusahaan tempat Anda bekerja.")
                ->line('Terima kasih telah memilih <strong>'.config('app.name').'.</strong>');
        } else {
            // Email content for companies
            return (new MailMessage)
                ->greeting('Yth, '.$notifiable->name)
                ->subject("Pelamar Baru untuk Loker {$this->job->title}")
                ->line("{$this->user->name} telah melamar untuk posisi {$this->job->title} di perusahaan Anda.")
                ->line("Silakan periksa lamaran ini di dashboard perusahaan Anda.")
                ->action('Lihat Lamaran', route('company.myjob'))
                ->line('Terima kasih telah menggunakan <strong>'.config('app.name').'.</strong>');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        if ($notifiable->role == 'candidate') {
            // Database notification content for candidates
            return [
                'title' => 'Anda telah melamar pekerjaan di '.$this->company->name,
                'url' => route('candidate.appliedjob'),
            ];
        } else {
            // Database notification content for companies
            return [
                'title' => ucfirst($this->user->name).' melamar lowongan kerja Anda.',
                'url' => route('company.myjob'),
            ];
        }
    }
}
