<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\ApplicationGroup;
use App\Models\AppliedJob;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Yajra\DataTables\Facades\DataTables;

class SimpleApplicationDataTableController extends Controller
{
    public function index(Request $request)
    {
        try {
            // Validasi parameter
            if (!$request->has('job') || !$request->has('group_id')) {
                return response()->json([
                    'error' => true,
                    'message' => 'Parameter job dan group_id diperlukan'
                ], 400);
            }

            $job_id = $request->job;
            $group_id = $request->group_id;

            // Cari job
            $job = Job::find($job_id);
            if (!$job) {
                return response()->json([
                    'error' => true,
                    'message' => 'Job tidak ditemukan'
                ], 404);
            }

            // Verifikasi kepemilikan job
            if (currentCompany()->id != $job->company_id) {
                return response()->json([
                    'error' => true,
                    'message' => 'Anda tidak memiliki akses ke job ini'
                ], 403);
            }

            // Mengoptimalkan query dengan select kolom yang diperlukan saja
            $query = AppliedJob::where('job_id', $job->id)
                ->select('applied_jobs.*')
                ->with([
                    'candidate' => function($q) {
                        $q->select('id', 'user_id', 'education_id', 'experience_id');
                    },
                    'candidate.user' => function($q) {
                        $q->select('id', 'name', 'email', 'username', 'image');
                    },
                    'candidate.education',
                    'candidate.experience',
                    'applicationGroup:id,name'
                ])
                ->where('application_group_id', $group_id);

            // Ambil hanya grup aplikasi milik perusahaan saat ini
            $groups = ApplicationGroup::where('company_id', currentCompany()->id)
                ->select('id', 'name')
                ->get();

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('checkbox', function ($row) {
                    return '<div class="form-check">
                        <input class="form-check-input application-checkbox" type="checkbox" value="' . $row->id . '" data-group="' . $row->application_group_id . '">
                    </div>';
                })
                ->addColumn('candidate', function ($row) {
                    $html = '<div class="d-flex align-items-center">';

                    if ($row->candidate && $row->candidate->user) {
                        $html .= '<div class="me-3">
                            <img src="' . $row->candidate->user->image_url . '" alt="Foto ' . $row->candidate->user->name . '" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                        </div>
                        <div>
                            <h6 class="mb-0">' . $row->candidate->user->name . '</h6>
                            <small class="text-muted">' . $row->candidate->user->email . '</small>
                        </div>';
                    }

                    $html .= '</div>';

                    return $html;
                })
                ->addColumn('education', function ($row) {
                    if ($row->candidate && $row->candidate->education) {
                        return $row->candidate->education->name;
                    } else if ($row->candidate && $row->candidate->education_id) {
                        // Jika education_id ada tapi relasi tidak terload dengan benar
                        $education = \App\Models\Education::find($row->candidate->education_id);
                        return $education ? $education->name : '<span class="text-muted">-</span>';
                    }

                    return '<span class="text-muted">-</span>';
                })
                ->addColumn('applied_date', function ($row) {
                    return formatIndonesianDate($row->created_at);
                })
                ->addColumn('status', function ($row) {
                    $html = '<span class="badge bg-';

                    if ($row->applicationGroup->name == 'Semua Lamaran') {
                        $html .= 'secondary';
                    } elseif ($row->applicationGroup->name == 'Interview') {
                        $html .= 'primary';
                    } elseif ($row->applicationGroup->name == 'Diterima') {
                        $html .= 'success';
                    } else {
                        $html .= 'danger';
                    }

                    $html .= '">' . $row->applicationGroup->name . '</span>';

                    if ($row->applicationGroup->name == 'Interview' && !empty($row->interview_details)) {
                        $html .= '<button type="button" class="btn btn-sm btn-link p-0 ms-1 view-interview-details"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Lihat Detail Interview"
                            data-application-id="' . $row->id . '"
                            data-interview-date="' . ($row->interview_details['date'] ?? '') . '"
                            data-interview-time="' . ($row->interview_details['time'] ?? '') . '"
                            data-interview-location="' . ($row->interview_details['location'] ?? '') . '"
                            data-interview-notes="' . ($row->interview_details['notes'] ?? '') . '">
                            <i class="ph-info"></i>
                        </button>';
                    }

                    return $html;
                })
                ->addColumn('action', function ($row) use ($groups) {
                    $html = '<div class="btn-group">';

                    // View profile button
                    $html .= '<button type="button" class="btn btn-sm btn-outline-primary view-profile"
                        data-username="' . ($row->candidate->user->username ?? '') . '" data-bs-toggle="tooltip" title="Lihat Profil">
                        <i class="ph-user"></i>
                    </button>';

                    // Download CV button
                    if ($row->candidate_resume_id) {
                        $html .= '<a href="' . route('website.candidate.download.cv', $row->candidate_resume_id) . '"
                            class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Unduh CV">
                            <i class="ph-download"></i>
                        </a>';

                        // Preview CV button
                        $html .= '<button type="button" class="btn btn-sm btn-outline-primary preview-cv"
                            data-resume-id="' . $row->candidate_resume_id . '" data-bs-toggle="tooltip" title="Pratinjau CV">
                            <i class="ph-eye"></i>
                        </button>';
                    }

                    // Move application dropdown
                    $html .= '<button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ph-arrows-counter-clockwise"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">';

                    // Gunakan data groups yang sudah difilter (hanya milik perusahaan saat ini)
                    foreach ($groups as $group) {
                        // Hanya tampilkan grup yang berbeda dengan grup saat ini
                        if ($group->id != $row->application_group_id) {
                            if ($group->name == 'Interview') {
                                $html .= '<li>
                                    <a href="#" class="dropdown-item schedule-interview"
                                        data-application-id="' . $row->id . '"
                                        data-group-id="' . $group->id . '">
                                        ' . $group->name . '
                                    </a>
                                </li>';
                            } else {
                                $html .= '<li>
                                    <a href="#" class="dropdown-item move-application"
                                        data-application-id="' . $row->id . '"
                                        data-group-id="' . $group->id . '"
                                        data-group-name="' . $group->name . '">
                                        ' . $group->name . '
                                    </a>
                                </li>';
                            }
                        }
                    }

                    $html .= '</ul></div>';

                    return $html;
                })
                ->rawColumns(['checkbox', 'candidate', 'education', 'status', 'action'])
                ->make(true);
        } catch (\Exception $e) {
            // Log error untuk debugging
            \Log::error('SimpleApplicationDataTableController error: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            // Kirim response error yang informatif
            return response()->json([
                'error' => true,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => config('app.debug') ? $e->getTrace() : null
            ], 500);
        }
    }
}
