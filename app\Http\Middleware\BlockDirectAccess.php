<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BlockDirectAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Illuminate\Http\Response
     */
    public function handle(Request $request, Closure $next)
    {
        // Cek apakah request datang dari aplikasi internal atau API dengan token
        if (!$request->header('X-API-KEY') || $request->header('X-API-KEY') !== env('API_KEY')) {
            // Jika tidak valid, kembalikan 403 Forbidden
            return response()->json(['message' => 'Forbidden'], 403);
        }

        return $next($request);
    }
}
