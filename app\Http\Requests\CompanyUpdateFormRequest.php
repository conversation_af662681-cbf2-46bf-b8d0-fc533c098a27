<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompanyUpdateFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // Bypass validasi unique untuk username dan email
        $id = $this->route('perusahaan');

        \Log::info('CompanyUpdateFormRequest: rules', [
            'id' => $id,
            'route_parameters' => $this->route()->parameters(),
            'request_method' => $this->method(),
            'request_path' => $this->path()
        ]);

        return [
            'name' => 'required',
            'username' => 'required',
            'email' => 'required|email',
            'organization_type_id' => 'required',
            'industry_type_id' => 'required',
            'team_size_id' => 'nullable',
            'establishment_year' => 'nullable|numeric|min:1900|max:' . date('Y'),
            'website' => 'nullable|url|max:255',
        ];
    }
}
