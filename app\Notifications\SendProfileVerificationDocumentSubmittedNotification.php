<?php

namespace App\Notifications;

use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SendProfileVerificationDocumentSubmittedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public Company $company;

    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        return (new MailMessage)
            ->subject('Pengajuan Verifikasi Dokumen Baru')
            ->line('Halo Admin,')
            ->line('Pengajuan verifikasi dokumen baru telah diterima di '.env('APP_NAME').'. oleh '.$this->company->user->name.'. Berikut rinciannya:')
            ->action('Lihat Dokumen', route('admin.company.documents', $this->company))
            ->line('Silakan masuk ke dasbor admin untuk meninjau dan memproses kiriman.')
            ->line('Terima kasih telah menjaga keamanan dan kredibilitas platform kami.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'title' => __('Dokumen verifikasi profil baru tersedia untuk disetujui'),
            'url' => route('admin.company.documents', $this->company),
        ];
    }
}
