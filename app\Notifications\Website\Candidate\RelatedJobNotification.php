<?php

namespace App\Notifications\Website\Candidate;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RelatedJobNotification extends Notification
{
    use Queueable;

    public $job;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($job)
    {
        $this->job = $job;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // return (new MailMessage)
        //     ->line('The introduction to the notification.')
        //     ->action('Notification Action', url('/'))
        //     ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        // Dapatkan prefix badan hukum dan nama perusahaan
        $company = $this->job?->company;
        $companyName = $this->job?->company?->user?->name;
        $organizationPrefix = '';

        // Jika perusahaan memiliki organization_type_id
        if ($company && $company->organization_type_id) {
            $organizationType = \App\Models\OrganizationTypeTranslation::where('organization_type_id', $company->organization_type_id)
                ->where('locale', 'id')
                ->first();

            if ($organizationType && $organizationType->prefix) {
                $organizationPrefix = $organizationType->prefix . ' ';
            }
        }

        // Gabungkan prefix dan nama perusahaan
        $fullCompanyName = $organizationPrefix . $companyName;

        return [
            'title' => 'Lowongan ' . $this->job->title . ' di ' . $fullCompanyName,
            'url' => route('website.job.details', $this->job->slug),
            'job_id' => $this->job->id,
            'company_name' => $fullCompanyName,
            'role_name' => $this->job?->role?->name,
        ];
    }
}
