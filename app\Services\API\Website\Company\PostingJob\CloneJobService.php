<?php

namespace App\Services\API\Website\Company\PostingJob;

use App\Models\Job;
use App\Models\Setting;
use F9Web\ApiResponseHelpers;

class CloneJobService
{
    use ApiResponseHelpers;

    public function execute($request){
        $job = Job::whereSlug($request->slug)->first();

        if (!$job) {
            return $this->respondNotFound(__('job_not_found'));
        }

        $newJob = $job->replicate();
        $newJob->created_at = now();

        $newJob->save();

        return $this->respondWithSuccess([
            'data' => [
                'data' => $newJob,
                'message' => __('job_cloned_successfully'),
            ]
        ]);
    }
}
