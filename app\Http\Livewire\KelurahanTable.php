<?php

namespace App\Http\Livewire;

use PowerComponents\LivewirePowerGrid\{Colum<PERSON>, Header, Footer, Exportable, PowerGridComponent};
use Illuminate\Database\Eloquent\Builder;
use App\Models\Kelurahan;

class KelurahanTable extends PowerGridComponent
{
    public function setUp(): array
    {
        return [
            Exportable::make('export')->striped(),
            Header::make()->showSearchInput(),
            Footer::make()->showPerPage(10)->showRecordCount(),
        ];
    }

    public function datasource(): Builder
    {
        return Kelurahan::query()->with('kecamatan');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')->sortable(),
            Column::make('Kelurahan', 'name')->sortable()->searchable(),
            Column::make('Kecamatan', 'kecamatan.name')->sortable()->searchable(),
        ];
    }
}
