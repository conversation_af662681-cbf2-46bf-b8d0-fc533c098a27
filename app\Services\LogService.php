<?php

namespace App\Services;

use App\Models\AdminLog;
use App\Models\LoginLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Jenssegers\Agent\Agent;

class LogService
{
    /**
     * Record login activity
     *
     * @param mixed $user
     * @param string $userType
     * @return LoginLog
     */
    public static function recordLogin($user, $userType = 'admin')
    {
        $ip = request()->ip();
        $location = self::getLocationFromIp($ip);
        $sessionId = Session::getId();

        // Check if Agent class exists
        if (class_exists('Jenssegers\Agent\Agent')) {
            $agent = new Agent();
            $device = $agent->device();
            $browser = $agent->browser();
        } else {
            // Fallback if Agent class is not available
            $device = 'Unknown';
            $browser = 'Unknown';
        }

        // Deactivate any active sessions for this user
        LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'logout_at' => now()
            ]);

        // Create new login log
        return LoginLog::create([
            'user_id' => $user->id,
            'user_type' => $userType,
            'ip_address' => $ip,
            'device' => $device,
            'browser' => $browser,
            'location' => $location,
            'login_at' => now(),
            'session_id' => $sessionId,
            'is_active' => true
        ]);
    }

    /**
     * Record logout activity
     *
     * @param mixed $user
     * @param string $userType
     * @return bool
     */
    public static function recordLogout($user = null, $userType = 'admin')
    {
        if (!$user && Auth::guard('admin')->check()) {
            $user = Auth::guard('admin')->user();
        } elseif (!$user) {
            return false;
        }

        $sessionId = Session::getId();

        return LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('session_id', $sessionId)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'logout_at' => now()
            ]);
    }

    /**
     * Record admin activity
     *
     * @param string $action
     * @param string $module
     * @param string $description
     * @return AdminLog|null
     */
    public static function recordAdminActivity($action, $module, $description)
    {
        if (!Auth::guard('admin')->check()) {
            return null;
        }

        $admin = Auth::guard('admin')->user();
        $ip = request()->ip();

        // Check if Agent class exists
        if (class_exists('Jenssegers\Agent\Agent')) {
            $agent = new Agent();
            $device = $agent->device();
            $browser = $agent->browser();
        } else {
            // Fallback if Agent class is not available
            $device = 'Unknown';
            $browser = 'Unknown';
        }

        return AdminLog::create([
            'admin_id' => $admin->id,
            'action' => $action,
            'module' => $module,
            'description' => $description,
            'ip_address' => $ip,
            'device' => $device,
            'browser' => $browser
        ]);
    }

    /**
     * Get location from IP address
     *
     * @param string $ip
     * @return string
     */
    private static function getLocationFromIp($ip)
    {
        try {
            // Use free IP geolocation API
            $response = file_get_contents("http://ip-api.com/json/{$ip}");
            $data = json_decode($response, true);

            if ($data && $data['status'] === 'success') {
                return $data['city'] . ', ' . $data['country'];
            }
        } catch (\Exception $e) {
            // Silently fail and return unknown
        }

        return 'Unknown';
    }

    /**
     * Check if user has another active session
     *
     * @param mixed $user
     * @param string $userType
     * @return bool
     */
    public static function hasAnotherActiveSession($user, $userType = 'admin')
    {
        $sessionId = Session::getId();

        return LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('session_id', '!=', $sessionId)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Force logout from all other sessions
     *
     * @param mixed $user
     * @param string $userType
     * @return bool
     */
    public static function forceLogoutOtherSessions($user, $userType = 'admin')
    {
        $sessionId = Session::getId();

        return LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('session_id', '!=', $sessionId)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'logout_at' => now()
            ]);
    }
}
