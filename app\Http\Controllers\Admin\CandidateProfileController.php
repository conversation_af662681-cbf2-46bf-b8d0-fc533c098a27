<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class CandidateProfileController extends Controller
{
    /**
     * Get candidate profile details for admin
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfileDetails(Request $request)
    {
        try {
            $candidate = User::where('username', $request->username)
                ->where('role', 'candidate')
                ->with([
                    'contactInfo', 
                    'socialInfo', 
                    'candidate' => function ($query) {
                        $query->with('experience', 'education', 'experiences', 'educations', 'profession', 'languages:id,name', 'skills');
                    }
                ])
                ->firstOrFail();

            // Format birth date if exists
            if ($candidate->candidate && $candidate->candidate->birth_date) {
                $candidate->candidate->birth_date = date('d-m-Y', strtotime($candidate->candidate->birth_date));
            }

            // Get skills as comma-separated string
            $skills = $candidate->candidate->skills->pluck('name');
            $candidate_skills = $skills ? implode(', ', json_decode(json_encode($skills))) : '';

            // Get languages as comma-separated string
            $languages = $candidate->candidate->languages->pluck('name');
            $candidate_languages = $languages ? implode(', ', json_decode(json_encode($languages))) : '';

            // Remove sensitive information
            if (isset($candidate->candidate->ktp_file)) {
                unset($candidate->candidate->ktp_file);
            }

            return response()->json([
                'success' => true,
                'data' => $candidate,
                'skills' => $candidate_skills,
                'languages' => $candidate_languages,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ]);
        }
    }
}
