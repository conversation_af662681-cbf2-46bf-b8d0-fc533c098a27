<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Illuminate\Support\Collection;

class ReportsExport implements WithMultipleSheets
{
    protected $data;
    protected $period;
    protected $location;

    public function __construct($data, $period, $location)
    {
        $this->data = $data;
        $this->period = $period;
        $this->location = $location;
    }

    public function sheets(): array
    {
        $sheets = [];
        
        foreach ($this->data as $yearData) {
            foreach ($yearData['reports'] as $locationData) {
                // Tambahkan sheet untuk perbandingan jumlah pencaker dan perusahaan
                if (isset($locationData['data']['user_comparison'])) {
                    $sheets[] = new UserComparisonSheet(
                        $yearData['year'],
                        $locationData['location'],
                        $locationData['data']['user_comparison']
                    );
                }
                
                // Tambahkan sheet untuk data loker per bulan
                if (isset($locationData['data']['jobs_by_month'])) {
                    $sheets[] = new JobsByMonthSheet(
                        $yearData['year'],
                        $locationData['location'],
                        $locationData['data']['jobs_by_month']
                    );
                }
                
                // Tambahkan sheet untuk status lamaran kerja
                if (isset($locationData['data']['application_status'])) {
                    $sheets[] = new ApplicationStatusSheet(
                        $yearData['year'],
                        $locationData['location'],
                        $locationData['data']['application_status']
                    );
                }
                
                // Tambahkan sheet untuk distribusi pendidikan
                if (isset($locationData['data']['education_distribution'])) {
                    $sheets[] = new EducationDistributionSheet(
                        $yearData['year'],
                        $locationData['location'],
                        $locationData['data']['education_distribution']
                    );
                }
                
                // Tambahkan sheet untuk distribusi umur
                if (isset($locationData['data']['age_distribution'])) {
                    $sheets[] = new AgeDistributionSheet(
                        $yearData['year'],
                        $locationData['location'],
                        $locationData['data']['age_distribution']
                    );
                }
                
                // Tambahkan sheet untuk tren lamaran kerja
                if (isset($locationData['data']['application_trends'])) {
                    $sheets[] = new ApplicationTrendsSheet(
                        $yearData['year'],
                        $locationData['location'],
                        $locationData['data']['application_trends']
                    );
                }
            }
        }
        
        return $sheets;
    }
}

class UserComparisonSheet implements FromCollection, WithHeadings, WithTitle, WithStyles, ShouldAutoSize
{
    protected $year;
    protected $location;
    protected $data;

    public function __construct($year, $location, $data)
    {
        $this->year = $year;
        $this->location = $location;
        $this->data = $data;
    }

    public function collection()
    {
        $rows = new Collection();
        
        // Data bulanan
        foreach ($this->data['months'] as $index => $month) {
            $rows->push([
                'Bulan' => $month,
                'Pencaker' => $this->data['candidates'][$index],
                'Perusahaan' => $this->data['companies'][$index],
                'Rasio' => $this->data['companies'][$index] > 0 
                    ? number_format($this->data['candidates'][$index] / $this->data['companies'][$index], 1) . ':1'
                    : 'N/A'
            ]);
        }
        
        // Tambahkan baris kosong
        $rows->push([]);
        
        // Tambahkan total
        $totalCandidates = array_sum($this->data['candidates']);
        $totalCompanies = array_sum($this->data['companies']);
        
        $rows->push([
            'Bulan' => 'Total',
            'Pencaker' => $totalCandidates,
            'Perusahaan' => $totalCompanies,
            'Rasio' => $totalCompanies > 0 
                ? number_format($totalCandidates / $totalCompanies, 1) . ':1'
                : 'N/A'
        ]);
        
        // Tambahkan data versus jika ada
        if (isset($this->data['versus_data'])) {
            // Tambahkan baris kosong
            $rows->push([]);
            $rows->push(['Bulan' => 'Perbandingan Lokasi']);
            $rows->push([]);
            
            $rows->push([
                'Bulan' => 'Lokasi',
                'Pencaker' => 'Total Pencaker',
                'Perusahaan' => 'Total Perusahaan',
                'Rasio' => 'Rasio'
            ]);
            
            foreach ($this->data['versus_data'] as $versus) {
                $rows->push([
                    'Bulan' => $versus['location'],
                    'Pencaker' => $versus['total_candidates'],
                    'Perusahaan' => $versus['total_companies'],
                    'Rasio' => $versus['total_companies'] > 0 
                        ? number_format($versus['total_candidates'] / $versus['total_companies'], 1) . ':1'
                        : 'N/A'
                ]);
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Bulan',
            'Pencaker',
            'Perusahaan',
            'Rasio'
        ];
    }

    public function title(): string
    {
        return 'Perbandingan Pencaker-Perusahaan ' . $this->location . ' ' . $this->year;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            $this->data['candidates'] ? count($this->data['candidates']) + 3 : 3 => ['font' => ['bold' => true]],
        ];
    }
}

class JobsByMonthSheet implements FromCollection, WithHeadings, WithTitle, WithStyles, ShouldAutoSize
{
    protected $year;
    protected $location;
    protected $data;

    public function __construct($year, $location, $data)
    {
        $this->year = $year;
        $this->location = $location;
        $this->data = $data;
    }

    public function collection()
    {
        $rows = new Collection();
        
        // Data bulanan
        foreach ($this->data['months'] as $index => $month) {
            $rows->push([
                'Bulan' => $month,
                'Jumlah Loker' => $this->data['jobs'][$index]
            ]);
        }
        
        // Tambahkan baris kosong
        $rows->push([]);
        
        // Tambahkan total
        $totalJobs = array_sum($this->data['jobs']);
        $avgJobs = count($this->data['jobs']) > 0 ? $totalJobs / count($this->data['jobs']) : 0;
        
        $rows->push([
            'Bulan' => 'Total',
            'Jumlah Loker' => $totalJobs
        ]);
        
        $rows->push([
            'Bulan' => 'Rata-rata per Bulan',
            'Jumlah Loker' => number_format($avgJobs, 1)
        ]);
        
        // Tambahkan data versus jika ada
        if (isset($this->data['versus_data'])) {
            // Tambahkan baris kosong
            $rows->push([]);
            $rows->push(['Bulan' => 'Perbandingan Lokasi']);
            $rows->push([]);
            
            $rows->push([
                'Bulan' => 'Lokasi',
                'Jumlah Loker' => 'Total Loker'
            ]);
            
            foreach ($this->data['versus_data'] as $versus) {
                $rows->push([
                    'Bulan' => $versus['location'],
                    'Jumlah Loker' => $versus['total_jobs']
                ]);
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Bulan',
            'Jumlah Loker'
        ];
    }

    public function title(): string
    {
        return 'Data Loker per Bulan ' . $this->location . ' ' . $this->year;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            count($this->data['jobs']) + 3 => ['font' => ['bold' => true]],
            count($this->data['jobs']) + 4 => ['font' => ['bold' => true]],
        ];
    }
}

class ApplicationStatusSheet implements FromCollection, WithHeadings, WithTitle, WithStyles, ShouldAutoSize
{
    protected $year;
    protected $location;
    protected $data;

    public function __construct($year, $location, $data)
    {
        $this->year = $year;
        $this->location = $location;
        $this->data = $data;
    }

    public function collection()
    {
        $rows = new Collection();
        
        // Data status
        $totalApplications = 0;
        foreach ($this->data['statusData'] as $status) {
            $totalApplications += $status['count'];
            $rows->push([
                'Status' => $status['name'],
                'Jumlah' => $status['count']
            ]);
        }
        
        // Tambahkan baris kosong
        $rows->push([]);
        
        // Tambahkan total
        $rows->push([
            'Status' => 'Total',
            'Jumlah' => $totalApplications
        ]);
        
        // Tambahkan data versus jika ada
        if (isset($this->data['versus_data'])) {
            // Tambahkan baris kosong
            $rows->push([]);
            $rows->push(['Status' => 'Perbandingan Lokasi']);
            $rows->push([]);
            
            $rows->push([
                'Status' => 'Lokasi',
                'Jumlah' => 'Total Lamaran'
            ]);
            
            foreach ($this->data['versus_data'] as $versus) {
                $rows->push([
                    'Status' => $versus['location'],
                    'Jumlah' => $versus['total_applications']
                ]);
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Status',
            'Jumlah'
        ];
    }

    public function title(): string
    {
        return 'Status Lamaran ' . $this->location . ' ' . $this->year;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            count($this->data['statusData']) + 3 => ['font' => ['bold' => true]],
        ];
    }
}

class EducationDistributionSheet implements FromCollection, WithHeadings, WithTitle, WithStyles, ShouldAutoSize
{
    protected $year;
    protected $location;
    protected $data;

    public function __construct($year, $location, $data)
    {
        $this->year = $year;
        $this->location = $location;
        $this->data = $data;
    }

    public function collection()
    {
        $rows = new Collection();
        
        // Data pendidikan
        $totalCandidates = 0;
        foreach ($this->data['educationData'] as $education) {
            $totalCandidates += $education['count'];
            $rows->push([
                'Pendidikan' => $education['pendidikan_terakhir'],
                'Jumlah' => $education['count']
            ]);
        }
        
        // Tambahkan baris kosong
        $rows->push([]);
        
        // Tambahkan total
        $rows->push([
            'Pendidikan' => 'Total',
            'Jumlah' => $totalCandidates
        ]);
        
        // Tambahkan data versus jika ada
        if (isset($this->data['versus_data'])) {
            // Tambahkan baris kosong
            $rows->push([]);
            $rows->push(['Pendidikan' => 'Perbandingan Lokasi']);
            $rows->push([]);
            
            $rows->push([
                'Pendidikan' => 'Lokasi',
                'Jumlah' => 'Total Pencaker'
            ]);
            
            foreach ($this->data['versus_data'] as $versus) {
                $rows->push([
                    'Pendidikan' => $versus['location'],
                    'Jumlah' => $versus['total_candidates']
                ]);
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Pendidikan',
            'Jumlah'
        ];
    }

    public function title(): string
    {
        return 'Distribusi Pendidikan ' . $this->location . ' ' . $this->year;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            count($this->data['educationData']) + 3 => ['font' => ['bold' => true]],
        ];
    }
}

class AgeDistributionSheet implements FromCollection, WithHeadings, WithTitle, WithStyles, ShouldAutoSize
{
    protected $year;
    protected $location;
    protected $data;

    public function __construct($year, $location, $data)
    {
        $this->year = $year;
        $this->location = $location;
        $this->data = $data;
    }

    public function collection()
    {
        $rows = new Collection();
        
        // Data umur
        $totalCandidates = 0;
        foreach ($this->data['ageData'] as $age) {
            $totalCandidates += $age['count'];
            $rows->push([
                'Kelompok Umur' => $age['age_group'],
                'Jumlah' => $age['count']
            ]);
        }
        
        // Tambahkan baris kosong
        $rows->push([]);
        
        // Tambahkan total
        $rows->push([
            'Kelompok Umur' => 'Total',
            'Jumlah' => $totalCandidates
        ]);
        
        // Tambahkan data versus jika ada
        if (isset($this->data['versus_data'])) {
            // Tambahkan baris kosong
            $rows->push([]);
            $rows->push(['Kelompok Umur' => 'Perbandingan Lokasi']);
            $rows->push([]);
            
            $rows->push([
                'Kelompok Umur' => 'Lokasi',
                'Jumlah' => 'Total Pencaker'
            ]);
            
            foreach ($this->data['versus_data'] as $versus) {
                $rows->push([
                    'Kelompok Umur' => $versus['location'],
                    'Jumlah' => $versus['total_candidates']
                ]);
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Kelompok Umur',
            'Jumlah'
        ];
    }

    public function title(): string
    {
        return 'Distribusi Umur ' . $this->location . ' ' . $this->year;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            count($this->data['ageData']) + 3 => ['font' => ['bold' => true]],
        ];
    }
}

class ApplicationTrendsSheet implements FromCollection, WithHeadings, WithTitle, WithStyles, ShouldAutoSize
{
    protected $year;
    protected $location;
    protected $data;

    public function __construct($year, $location, $data)
    {
        $this->year = $year;
        $this->location = $location;
        $this->data = $data;
    }

    public function collection()
    {
        $rows = new Collection();
        
        // Data tren
        foreach ($this->data['months'] as $index => $month) {
            $rows->push([
                'Bulan' => $month,
                'Jumlah Lamaran' => $this->data['trendsData'][$index]
            ]);
        }
        
        // Tambahkan baris kosong
        $rows->push([]);
        
        // Tambahkan total dan rata-rata
        $totalApplications = array_sum($this->data['trendsData']);
        $avgApplications = count($this->data['trendsData']) > 0 ? $totalApplications / count($this->data['trendsData']) : 0;
        
        $rows->push([
            'Bulan' => 'Total',
            'Jumlah Lamaran' => $totalApplications
        ]);
        
        $rows->push([
            'Bulan' => 'Rata-rata per Bulan',
            'Jumlah Lamaran' => number_format($avgApplications, 1)
        ]);
        
        // Tambahkan data versus jika ada
        if (isset($this->data['versus_data'])) {
            // Tambahkan baris kosong
            $rows->push([]);
            $rows->push(['Bulan' => 'Perbandingan Lokasi']);
            $rows->push([]);
            
            $rows->push([
                'Bulan' => 'Lokasi',
                'Jumlah Lamaran' => 'Total Lamaran'
            ]);
            
            foreach ($this->data['versus_data'] as $versus) {
                $rows->push([
                    'Bulan' => $versus['location'],
                    'Jumlah Lamaran' => $versus['total_applications']
                ]);
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Bulan',
            'Jumlah Lamaran'
        ];
    }

    public function title(): string
    {
        return 'Tren Lamaran ' . $this->location . ' ' . $this->year;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            count($this->data['trendsData']) + 3 => ['font' => ['bold' => true]],
            count($this->data['trendsData']) + 4 => ['font' => ['bold' => true]],
        ];
    }
}
