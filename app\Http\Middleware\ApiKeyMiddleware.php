<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ApiKeyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Mendapatkan API key dari header 'x-api-key'
        $apiKey = $request->header('x-api-key');
    
        // Membandingkan dengan API key yang ada di konfigurasi .env atau app.php
        if ($apiKey !== config('app.api_key')) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
    
        return $next($request);
    }    
}