<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SuspendMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Arahkan semua permintaan ke halaman suspend.html
        return response()->file(public_path('suspend.html'));
    }
}
