<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Http\Traits\CandidateAble;
use App\Http\Traits\CandidateSkillAble;
use App\Http\Traits\HasCandidateResume;
use App\Models\AppliedJob;
use App\Models\Candidate;
use App\Models\CandidateLanguage;
use App\Models\CandidateResume;
use App\Models\Company;
use App\Models\ContactInfo;
use App\Models\Education;
use App\Models\Experience;
use App\Models\JobRole;
use App\Models\Profession;
use App\Models\Skill;
use App\Services\Website\Candidate\CandidateSettingUpdateService;
use App\Services\Website\Candidate\DashboardService;
use Illuminate\Http\Request;

class CandidateController extends Controller
{
    use CandidateAble, CandidateSkillAble, HasCandidateResume;

    public function __construct()
    {
        $this->middleware('access_limitation')->only([
            'settingUpdate',
        ]);
    }

    /**
     * Dasbor pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard()
    {
        try {
            $data = (new DashboardService())->execute();

            return view('frontend.pages.candidate.dashboard', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Halaman semua notifikasi pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function allNotification()
    {
        try {
            $notifications = auth()
                ->user()
                ->notifications()
                ->paginate(12);

            return view('frontend.pages.candidate.all-notification', compact('notifications'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Halaman pemberitahuan loker pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function jobAlerts()
    {
        try {
            // Periksa apakah pengguna memiliki job alert
            $candidate = auth()->user()->candidate;
            $jobAlerts = $candidate->jobRoleAlerts;

            // Hapus cache notifikasi untuk memastikan format baru digunakan
            \Cache::forget('notifications_' . auth()->id());

            // Jika tidak ada job alert, buat otomatis berdasarkan profesi/peran pengguna
            if ($jobAlerts->count() == 0 && $candidate->profession_id) {
                // Buat job alert berdasarkan profesi pengguna
                \App\Models\CandidateJobAlert::create([
                    'candidate_id' => $candidate->id,
                    'job_role_id' => $candidate->profession_id
                ]);

                // Cari pekerjaan aktif dengan peran yang sama
                $matchingJobs = \App\Models\Job::where('status', 'active')
                    ->where('role_id', $candidate->profession_id)
                    ->where('deadline', '>=', now())
                    ->get();

                // Kirim notifikasi untuk setiap pekerjaan yang cocok
                foreach ($matchingJobs as $job) {
                    auth()->user()->notify(new \App\Notifications\Website\Candidate\RelatedJobNotification($job));
                }
            }

            $notifications = auth()
                ->user()
                ->notifications()
                ->where('type', 'App\Notifications\Website\Candidate\RelatedJobNotification')
                ->paginate(12);

            return view('frontend.pages.candidate.job-alerts', compact('notifications'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Buat job alert secara manual
     *
     * @return \Illuminate\Http\Response
     */
    public function createJobAlert()
    {
        try {
            $candidate = auth()->user()->candidate;

            // Hapus notifikasi lama
            auth()->user()->notifications()
                ->where('type', 'App\Notifications\Website\Candidate\RelatedJobNotification')
                ->delete();

            // Hapus cache notifikasi untuk memastikan format baru digunakan
            \Cache::forget('notifications_' . auth()->id());

            // Jika pengguna belum memiliki profesi, tampilkan pesan
            if (!$candidate->profession_id) {
                flashWarning('Silakan lengkapi profil Anda dengan menambahkan profesi untuk mendapatkan notifikasi pekerjaan yang sesuai.');
                return redirect()->route('candidate.setting');
            }

            // Buat job alert berdasarkan profesi pengguna jika belum ada
            \App\Models\CandidateJobAlert::firstOrCreate([
                'candidate_id' => $candidate->id,
                'job_role_id' => $candidate->profession_id
            ]);

            // Cari pekerjaan aktif dengan peran yang sama
            $matchingJobs = \App\Models\Job::where('status', 'active')
                ->where('role_id', $candidate->profession_id)
                ->where('deadline', '>=', now())
                ->get();

            // Kirim notifikasi untuk setiap pekerjaan yang cocok
            foreach ($matchingJobs as $job) {
                auth()->user()->notify(new \App\Notifications\Website\Candidate\RelatedJobNotification($job));
            }

            if ($matchingJobs->count() > 0) {
                flashSuccess('Berhasil! ' . $matchingJobs->count() . ' lowongan kerja yang sesuai dengan profesi Anda telah ditambahkan ke notifikasi.');
            } else {
                flashInfo('Saat ini tidak ada lowongan kerja yang sesuai dengan profesi Anda. Kami akan memberi tahu Anda ketika ada lowongan baru.');
            }

            return redirect()->route('candidate.job.alerts');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());
            return back();
        }
    }

    /**
     * Halaman loker yang dilamar
     *
     * @return \Illuminate\Http\Renderable
     */
    public function appliedjobs(Request $request)
    {
        try {
            $candidate = Candidate::where('user_id', auth()->id())->first();
            if (empty($candidate)) {
                $candidate = new Candidate();
                $candidate->user_id = auth()->id();
                $candidate->save();
            }

            $resumes = CandidateResume::where('candidate_id', $candidate->id)->get();
            $applied_jobs = AppliedJob::with('applicationGroup:id,name')
                ->where('candidate_id', $candidate->id)
                ->get(['id', 'candidate_id', 'job_id', 'candidate_resume_id', 'cover_letter', 'application_group_id', 'interview_details']);

            $sortOrder = $request->get('sort', 'desc');

            $appliedJobs = $candidate
                ->appliedJobs()
                ->orderBy('created_at', $sortOrder)
                ->paginate(4)
                ->through(function ($application) use ($applied_jobs, $resumes) {
                    $application_group = $applied_jobs->where('job_id', $application->id)->first();
                    $resume = $resumes->where('id', $application_group->candidate_resume_id)->first();
                    $application->application_status = $application_group->applicationGroup->name;
                    $application->cover_letter = $application_group->cover_letter;
                    $application->cv_file = $resume ? $resume->file : '';
                    $application->cv_name = $resume ? $resume->name : '';
                    $application->interview_details = $application_group->interview_details;

                    return $application;
                });

            return view('frontend.pages.candidate.applied-jobs', compact('appliedJobs'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Halaman bookmark pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function bookmarks(Request $request)
    {
        try {
            $candidate = Candidate::where('user_id', auth()->id())->first();
            if (empty($candidate)) {
                $candidate = new Candidate();
                $candidate->user_id = auth()->id();
                $candidate->save();
            }

            $jobs = $candidate
                ->bookmarkJobs()
                ->withCount([
                    'appliedJobs as applied' => function ($q) use ($candidate) {
                        $q->where('candidate_id', $candidate->id);
                    },
                ])
                ->paginate(10);

            if (auth('user')->check() && authUser()->role == 'candidate') {
                $resumes = currentCandidate()->resumes;
            } else {
                $resumes = [];
            }

            return view('frontend.pages.candidate.bookmark', compact('jobs', 'resumes'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Toggle pelamar untuk membookmark perusahaan
     *
     * @return \Illuminate\Http\Response
     */
    public function bookmarkCompany(Company $company)
    {
        try {
            $company->bookmarkCandidateCompany()->toggle(currentCandidate());

            return back();
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Halaman pengaturan pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function setting()
    {
        try {
            $candidate = auth()->user()->candidate;

            if (empty($candidate)) {
                Candidate::create(['user_id' => auth()->id()]);
            }

            // kontak
            $contactInfo = ContactInfo::where('user_id', auth()->id())->first();
            $contact = [];
            if ($contactInfo) {
                $contact = $contactInfo;
            } else {
                $contact = '';
            }

            // medsos
            $socials = auth()->user()->socialInfo;

            // resume/cv
            $resumes = $candidate->resumes;

            $job_roles = JobRole::all()->sortBy('name');
            $experiences = Experience::all();
            $educations = Education::all();
            $professions = Profession::all()->sortBy('name');
            $skills = Skill::all()->sortBy('name');
            $languages = CandidateLanguage::all(['id', 'name']);
            $candidate->load('skills', 'languages', 'experiences', 'educations', 'jobRoleAlerts:id,candidate_id,job_role_id');

            return view('frontend.pages.candidate.setting', [
                'candidate' => $candidate->load('skills', 'languages'),
                'contact' => $contact,
                'socials' => $socials,
                'job_roles' => $job_roles,
                'experiences' => $experiences,
                'educations' => $educations,
                'professions' => $professions,
                'resumes' => $resumes,
                'skills' => $skills,
                'candidate_languages' => $languages,
            ]);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Update pengaturan pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function settingUpdate(Request $request)
    {
        try {
            (new CandidateSettingUpdateService())->update($request);

            return back();
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Update username pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function usernameUpdate(Request $request)
    {
        try {
            $request->session()->put('type', 'account');

            if ($request->type == 'candidate_username') {
                $request->validate([
                    'username' => 'required|unique:users,username,'.auth()->user()->id,
                ]);

                authUser()->update([
                    'username' => $request->username,
                ]);

                flashSuccess(__('username_updated_successfully'));

                return back();
            }
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

}
