<?php

namespace App\Console\Commands;

use App\Models\AppliedJob;
use App\Models\ApplicationGroup;
use App\Models\DefaultApplicationGroup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateApplicationGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-application-groups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing application groups to default application groups';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration of application groups...');
        
        // Get all applied jobs
        $appliedJobs = AppliedJob::whereNull('default_application_group_id')->get();
        $this->info('Found ' . $appliedJobs->count() . ' applied jobs to migrate');
        
        $bar = $this->output->createProgressBar($appliedJobs->count());
        $bar->start();
        
        $updated = 0;
        $skipped = 0;
        
        foreach ($appliedJobs as $appliedJob) {
            // Get the application group
            $applicationGroup = ApplicationGroup::find($appliedJob->application_group_id);
            
            if ($applicationGroup) {
                // Find corresponding default application group
                $defaultGroup = DefaultApplicationGroup::where('name', $applicationGroup->name)->first();
                
                if ($defaultGroup) {
                    $appliedJob->update([
                        'default_application_group_id' => $defaultGroup->id,
                    ]);
                    $updated++;
                } else {
                    // Try to find a similar default group
                    if (str_contains(strtolower($applicationGroup->name), 'diterima') || 
                        str_contains(strtolower($applicationGroup->name), 'accept')) {
                        $defaultGroup = DefaultApplicationGroup::where('name', 'Diterima')->first();
                    } elseif (str_contains(strtolower($applicationGroup->name), 'interview')) {
                        $defaultGroup = DefaultApplicationGroup::where('name', 'Interview')->first();
                    } elseif (str_contains(strtolower($applicationGroup->name), 'ditolak') || 
                             str_contains(strtolower($applicationGroup->name), 'tolak') || 
                             str_contains(strtolower($applicationGroup->name), 'reject')) {
                        $defaultGroup = DefaultApplicationGroup::where('name', 'Ditolak')->first();
                    } else {
                        $defaultGroup = DefaultApplicationGroup::where('name', 'Semua Lamaran')->first();
                    }
                    
                    if ($defaultGroup) {
                        $appliedJob->update([
                            'default_application_group_id' => $defaultGroup->id,
                        ]);
                        $updated++;
                    } else {
                        $skipped++;
                    }
                }
            } else {
                // If no application group, set to 'Semua Lamaran'
                $defaultGroup = DefaultApplicationGroup::where('name', 'Semua Lamaran')->first();
                
                if ($defaultGroup) {
                    $appliedJob->update([
                        'default_application_group_id' => $defaultGroup->id,
                    ]);
                    $updated++;
                } else {
                    $skipped++;
                }
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        
        $this->info('Migration completed:');
        $this->info('- Updated: ' . $updated);
        $this->info('- Skipped: ' . $skipped);
    }
}
