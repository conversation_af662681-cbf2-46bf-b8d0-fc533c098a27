<?php

namespace App\Http\Traits;

use App\Models\CandidateEducation;
use App\Models\CandidateExperience;
use Illuminate\Http\Request;

trait CandidateSkillAble
{
    public function experienceStore(Request $request)
    {
        $request->session()->put('type', 'experience');

        // <PERSON><PERSON> fresh graduate, tidak perlu validasi field pengalaman
        if ($request->fresh_graduate) {
            $request->validate([
                'fresh_graduate' => 'required|boolean',
            ]);
        } else {
            $request->validate([
                'company' => 'required',
                'department' => 'required',
                'designation' => 'required',
                'start' => 'required',
                'end' => 'sometimes',
            ]);
        }

        $start_date = $request->start ? formatTime($request->start, 'Y-m-d') : null;
        $end_date = $request->end ? formatTime($request->end, 'Y-m-d') : null;

        CandidateExperience::create([
            'candidate_id' => currentCandidate()->id,
            'company' => $request->fresh_graduate ? null : $request->company,
            'department' => $request->fresh_graduate ? null : $request->department,
            'designation' => $request->fresh_graduate ? null : $request->designation,
            'start' => $request->fresh_graduate ? null : $start_date,
            'end' => $request->fresh_graduate ? null : $end_date,
            'responsibilities' => $request->fresh_graduate ? null : $request->responsibilities,
            'currently_working' => $request->fresh_graduate ? 0 : ($request->currently_working ?? 0),
            'fresh_graduate' => $request->fresh_graduate ?? 0,
        ]);

        return back()->with('success', 'Experience added successfully');
    }

    public function experienceUpdate(Request $request)
    {

        $request->session()->put('type', 'experience');

        // Jika fresh graduate, tidak perlu validasi field pengalaman
        if ($request->fresh_graduate) {
            $request->validate([
                'fresh_graduate' => 'required|boolean',
            ]);
        } else {
            $request->validate([
                'company' => 'required',
                'designation' => 'required',
                'department' => 'required',
                'start' => 'required',
                'end' => 'sometimes',
            ]);
        }

        $experience = CandidateExperience::findOrFail($request->experience_id);

        $start_date = $request->start ? formatTime($request->start, 'Y-m-d') : null;
        $end_date = $request->end ? formatTime($request->end, 'Y-m-d') : null;

        $experience->update([
            'candidate_id' => currentCandidate()->id,
            'company' => $request->fresh_graduate ? null : $request->company,
            'department' => $request->fresh_graduate ? null : $request->department,
            'designation' => $request->fresh_graduate ? null : $request->designation,
            'start' => $request->fresh_graduate ? null : $start_date,
            'end' => $request->fresh_graduate ? null : $end_date,
            'responsibilities' => $request->fresh_graduate ? null : $request->responsibilities,
            'currently_working' => $request->fresh_graduate ? 0 : ($request->currently_working ?? 0),
            'fresh_graduate' => $request->fresh_graduate ?? 0,
        ]);

        return back()->with('success', 'Experience updated successfully');
    }

    public function experienceDelete(CandidateExperience $experience)
    {
        session()->put('type', 'experience');

        $experience->delete();

        return back()->with('success', 'Experience deleted successfully');
    }

    public function educationStore(Request $request)
    {
        $request->session()->put('type', 'experience');

        $request->validate([
            'level' => 'required',
            'degree' => 'required',
            'year' => 'required',
        ]);

        CandidateEducation::create([
            'candidate_id' => currentCandidate()->id,
            'level' => $request->level,
            'degree' => $request->degree,
            'year' => $request->year,
            'notes' => $request->notes,
        ]);

        return back()->with('success', 'Education added successfully');
    }

    public function educationUpdate(Request $request)
    {
        $request->session()->put('type', 'experience');

        $request->validate([
            'level' => 'required',
            'degree' => 'required',
            'year' => 'required',
        ]);

        $education = CandidateEducation::findOrFail($request->education_id);

        $education->update([
            'candidate_id' => currentCandidate()->id,
            'level' => $request->level,
            'degree' => $request->degree,
            'year' => $request->year,
            'notes' => $request->notes,
        ]);

        return back()->with('success', 'Education updated successfully');
    }

    public function educationDelete(CandidateEducation $education)
    {
        session()->put('type', 'experience');

        $education->delete();

        return back()->with('success', 'Education deleted successfully');
    }
}
