<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CompanyCreateFormRequest;
use App\Http\Requests\CompanyUpdateFormRequest;
use App\Models\Company;
use App\Models\IndustryType;
use App\Models\OrganizationType;
use App\Models\TeamSize;
use App\Models\User;
use App\Notifications\SendProfileVerifiedNotification;
use App\Services\Admin\Company\CompanyCreateService;
use App\Services\Admin\Company\CompanyListService;
use App\Services\Admin\Company\CompanyUpdateService;
use Illuminate\Http\Request;
use Modules\Location\Entities\Country;

class CompanyController extends Controller
{
    public function index(Request $request)
    {
        try {
            abort_if(! userCan('company.view'), 403);

            // Filter berdasarkan status jika ada
            if ($request->has('status')) {
                if ($request->status === 'active') {
                    $request->merge(['user_status' => 1]);
                } elseif ($request->status === 'inactive') {
                    $request->merge(['user_status' => 0]);
                }
            }

            // Filter perusahaan dengan loker
            if ($request->has('has_jobs') && $request->has_jobs === 'true') {
                $request->merge(['with_jobs' => true]);
            }

            $companies = (new CompanyListService())->execute($request);

            // Get industry types with company count
            $industry_types = IndustryType::withCount('companies')->get()->sortBy(function($item) {
                return $item->name;
            });

            // Get organization types with company count
            $organization_types = OrganizationType::withCount('companies')->get()->sortBy(function($item) {
                return $item->name;
            });

            // Hitung statistik untuk card
            $totalCompanies = Company::count();
            $activeCompanies = Company::whereHas('user', function($q) {
                $q->where('status', 1);
            })->count();
            $inactiveCompanies = Company::whereHas('user', function($q) {
                $q->where('status', 0);
            })->count();

            // Hitung perusahaan dengan loker aktif
            $companiesWithJobs = Company::whereHas('jobs', function($q) {
                $q->where('status', 'active')
                  ->where('deadline', '>=', now()->toDateString());
            })->count();

            // Determine which view to use - default ke tampilan lama
            $view = $request->view === 'new' ? 'index_new' : 'index';

            // Jika ini adalah permintaan AJAX dan view adalah index_new
            if ($request->ajax() && $view === 'index_new') {
                return view("backend.company.{$view}", compact(
                    'companies',
                    'industry_types',
                    'organization_types',
                    'totalCompanies',
                    'activeCompanies',
                    'inactiveCompanies',
                    'companiesWithJobs'
                ));
            }

            return view("backend.company.{$view}", compact(
                'companies',
                'industry_types',
                'organization_types',
                'totalCompanies',
                'activeCompanies',
                'inactiveCompanies',
                'companiesWithJobs'
            ));
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try {
            abort_if(! userCan('company.create'), 403);

            $data['countries'] = Country::all();
            $data['industry_types'] = IndustryType::all()->sortBy('name');
            $data['organization_types'] = OrganizationType::all()->sortBy('name');
            $data['team_sizes'] = TeamSize::all();

            return view('backend.company.create', $data);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(CompanyCreateFormRequest $request)
    {
        try {
            abort_if(! userCan('company.create'), 403);

            (new CompanyCreateService())->execute($request);

            flashSuccess(__('company_created_successfully'));

            return redirect()->route('company.index');
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            abort_if(! userCan('company.view'), 403);

            $company = Company::with([
                'jobs.appliedJobs',
                'user.socialInfo',
                'user.contactInfo',
                'jobs' => function ($job) {
                    return $job->latest()->with('category', 'role', 'job_type', 'salary_type');
                },
            ])->findOrFail($id);

            return view('backend.company.show', compact('company'));
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            abort_if(! userCan('company.update'), 403);

            $data['company'] = Company::findOrFail($id);
            $data['user'] = $data['company']->user->load('socialInfo');
            $data['industry_types'] = IndustryType::all()->sortBy('name');
            $data['organization_types'] = OrganizationType::all()->sortBy('name');
            $data['team_sizes'] = TeamSize::all();
            $data['socials'] = $data['company']->user->socialInfo;

            return view('backend.company.edit', $data);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function update(CompanyUpdateFormRequest $request, $id)
    {
        try {
            abort_if(! userCan('company.update'), 403);

            // Ambil company berdasarkan ID
            $company = Company::find($id);

            if (!$company) {
                \Log::error('CompanyController: Company not found', [
                    'id' => $id
                ]);
                flashError('Perusahaan tidak ditemukan');
                return back();
            }

            // Debugging
            \Log::info('CompanyController: update method called', [
                'company_id' => $company->id,
                'request_data' => $request->all()
            ]);

            (new CompanyUpdateService())->execute($request, $company);

            // Refresh company data from database
            $refreshedCompany = Company::with('user')->find($company->id);

            // Debugging
            if ($refreshedCompany) {
                \Log::info('CompanyController: after update', [
                    'company_id' => $refreshedCompany->id,
                    'establishment_date' => $refreshedCompany->establishment_date,
                    'industry_type_id' => $refreshedCompany->industry_type_id,
                    'organization_type_id' => $refreshedCompany->organization_type_id
                ]);
            } else {
                \Log::warning('CompanyController: Company not found after refresh');
            }

            flashSuccess(__('company_updated_successfully'));

            return redirect()->route('company.index');
        } catch (\Exception $e) {
            \Log::error('CompanyController: Error in update', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            abort_if(! userCan('company.delete'), 403);

            $company = Company::findOrFail($id);

            // hapus gambar perusahaan: Logo, Banner, User
            deleteFile($company->logo);
            deleteFile($company->banner);
            deleteFile($company->user->image);

            // company cv view items delete
            $company->cv_views()->delete();
            $company->user->delete();
            $company->delete();

            flashSuccess(__('company_deleted_successfully'));

            return back();
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    public function documents(Company $company)
    {
        try {
            $company = $company->load('media');

            return view('backend.company.document', [
                'company' => $company,
            ]);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    public function downloadDocument(Request $request, Company $company)
    {
        try {
            $request->validate([
                'file_type' => 'required',
            ]);
            $media = $company->getFirstMedia($request->get('file_type'));

            return response()->download($media->getPath(), $media->file_name);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Ubah status perusahaan
     *
     * @return void
     */
    public function statusChange(Request $request)
    {
        try {
            $user = User::findOrFail($request->id);

            $user->update(['status' => $request->status]);

            if ($request->status == 1) {
                return responseSuccess(__('company_activated_successfully'));
            } else {
                return responseSuccess(__('company_deactivated_successfully'));
            }
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Ubah status verifikasi email perusahaan
     *
     * @return void
     */
    public function verificationChange(Request $request)
    {
        try {
            $user = User::findOrFail($request->id);

            if ($request->status) {
                $user->update(['email_verified_at' => now()]);
                $message = __('email_verified_successfully');
            } else {
                $user->update(['email_verified_at' => null]);
                $message = __('email_unverified_successfully');
            }

            return responseSuccess($message);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Ubah status verifikasi profil perusahaan
     *
     * @return void
     */
    public function profileVerificationChange(Request $request)
    {
        try {
            $company = Company::findOrFail($request->id);

            if ($request->status) {

                $company->document_verified_at = now();
                $company->update(['is_profile_verified' => true]);
                $company->user->notify(new SendProfileVerifiedNotification());
                $message = __('profile_verified_successfully');
            } else {

                $company->document_verified_at = null;
                $company->update(['is_profile_verified' => false]);
                $message = __('profile_unverified_successfully');
            }

            return responseSuccess($message);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Ubah status verifikasi dokumen perusahaan
     *
     * @param  Request  $request
     * @return void
     */
    public function toggle(Company $company)
    {
        try {
            if ($company->document_verified_at) {
                $company->update(['is_profile_verified' => false]);
                $company->document_verified_at = null;
                $message = __('unverified').' '.__('successfully');
            } else {
                $company->document_verified_at = now();
                $company->update(['is_profile_verified' => true]);
                $company->user->notify(new SendProfileVerifiedNotification());
                $message = __('verified').' '.__('successfully');
            }

            $company->save();

            return responseSuccess($message);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Get company jobs for modal
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCompanyJobs(Request $request)
    {
        try {
            $companyId = $request->company_id;

            if (!$companyId) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID perusahaan tidak ditemukan'
                ], 400);
            }

            // Ambil semua lowongan kerja aktif dari perusahaan tanpa filter deadline
            $jobs = \App\Models\Job::with(['job_type', 'salary_type'])
                ->where('company_id', $companyId)
                ->withCount('applications')
                ->get();

            // Log untuk debugging
            \Log::info('CompanyController: getCompanyJobs', [
                'company_id' => $companyId,
                'jobs_count' => $jobs->count(),
                'jobs' => $jobs->pluck('title', 'id')->toArray(),
                'sql' => \App\Models\Job::where('company_id', $companyId)->toSql()
            ]);

            if ($jobs) {
                $jobs = $jobs->map(function ($job) {
                    // Format salary
                    if ($job->salary_mode === 'range') {
                        $job->salary_format = 'Rp ' . number_format($job->min_salary ?? 0, 0, ',', '.') .
                            ' - Rp ' . number_format($job->max_salary ?? 0, 0, ',', '.');
                    } else {
                        $job->salary_format = 'Rp ' . number_format($job->custom_salary ?? 0, 0, ',', '.');
                    }

                    return $job;
                });
            }

            return response()->json([
                'success' => true,
                'jobs' => $jobs,
                'count' => $jobs->count()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
