<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Models\User;
use App\Notifications\JobActionNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class JobDetailController extends Controller
{
    /**
     * Get job detail for modal
     */
    public function getJobDetail($id)
    {
        $job = Job::with(['company.user', 'category', 'role', 'tags', 'benefits', 'job_type.translations', 'education.translations'])
            ->findOrFail($id);

        return view('backend.Job.partials.job-detail', compact('job'));
    }

    /**
     * Publish a job
     */
    public function publishJob(Request $request)
    {
        $job = Job::findOrFail($request->job_id);
        $job->status = 'active';
        $job->save();

        // Kirim notifikasi ke perusahaan
        $user = $job->company->user;
        $notificationData = [
            'title' => 'Lowongan <PERSON>r<PERSON>',
            'message' => 'Lowongan kerja "' . $job->title . '" telah diterbitkan.',
            'url' => route('website.job.details', $job->slug),
            'job_id' => $job->id,
        ];

        Notification::send($user, new JobActionNotification($notificationData));

        return response()->json(['success' => true]);
    }

    /**
     * Request revision for a job
     */
    public function revisionJob(Request $request)
    {
        $request->validate([
            'job_id' => 'required',
            'reason' => 'required|string',
        ]);

        $job = Job::findOrFail($request->job_id);
        $job->status = 'pending';
        $job->save();

        // Kirim notifikasi ke perusahaan
        $user = $job->company->user;
        $notificationData = [
            'title' => 'Lowongan Kerja Perlu Revisi',
            'message' => 'Lowongan kerja "' . $job->title . '" perlu direvisi. Alasan: ' . $request->reason,
            'url' => route('company.job.edit', $job->id),
            'job_id' => $job->id,
        ];

        Notification::send($user, new JobActionNotification($notificationData));

        return response()->json(['success' => true]);
    }

    /**
     * Reject a job
     */
    public function rejectJob(Request $request)
    {
        $request->validate([
            'job_id' => 'required',
            'reason' => 'required|string',
        ]);

        $job = Job::findOrFail($request->job_id);
        $job->status = 'rejected';
        $job->save();

        // Kirim notifikasi ke perusahaan
        $user = $job->company->user;
        $notificationData = [
            'title' => 'Lowongan Kerja Ditolak',
            'message' => 'Lowongan kerja "' . $job->title . '" ditolak. Alasan: ' . $request->reason,
            'url' => route('company.job.edit', $job->id),
            'job_id' => $job->id,
        ];

        Notification::send($user, new JobActionNotification($notificationData));

        return response()->json(['success' => true]);
    }
}
