<?php

namespace App\Http\Controllers;

use App\Models\Kelurahan;
use App\Models\Kecamatan;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class Ke<PERSON>rahanController extends Controller
{
    public function getKelurahanData(Request $request)
    {
        $query = Kelurahan::with('kecamatan');

        return DataTables::of($query)
            ->addColumn('kecamatan_name', function ($kelurahan) {
                return $kelurahan->kecamatan->name ?? '-';
            })
            ->addColumn('actions', function ($kelurahan) {
                return '<a href="'.route('location.kelurahan.edit', $kelurahan->id).'" class="btn btn-primary">Edit</a>';
            })
            ->rawColumns(['actions'])
            ->make(true);
    }

    public function index(Request $request)
    {
        $query = Kelurahan::query();

        if ($request->filled('keyword')) {
            $query->where('name', 'like', '%' . $request->keyword . '%');
        }

        if ($request->filled('kecamatan')) {
            $query->where('kecamatan_id', $request->kecamatan);
        }

        $kelurahans = $query->with('kecamatan')->paginate(10);
        $kecamatans = Kecamatan::all();

        return view('backend.settings.pages.location.kelurahan.index', compact('kelurahans', 'kecamatans'));
    }

    public function create()
    {
        $kecamatans = Kecamatan::all();
        return view('backend.settings.pages.location.kelurahan.create', compact('kecamatans'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'kecamatan_id' => 'required|exists:kecamatan,id',
            'long' => 'nullable|numeric',
            'lat' => 'nullable|numeric',
        ]);

        Kelurahan::create($request->all());
        return redirect()->route('location.kelurahan.create')->with('success', 'Kelurahan berhasil ditambahkan.');
    }

    public function edit($id)
    {
        $kelurahan = Kelurahan::findOrFail($id);
        $kecamatans = Kecamatan::all();
        return view('backend.settings.pages.location.kelurahan.edit', compact('kelurahan', 'kecamatans'));
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'kecamatan_id' => 'required|exists:kecamatan,id',
        ]);

        $kelurahan = Kelurahan::findOrFail($id);

        try {
            $kelurahan->update($validated);

            return redirect()->route('location.kelurahan.index')
                ->with('success', 'Kelurahan berhasil diperbarui.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan saat memperbarui kelurahan.');
        }
    }

    public function destroy($id)
    {
        $kelurahan = Kelurahan::findOrFail($id);
        $kelurahan->delete();

        return redirect()->route('location.kelurahan.index')->with('success', 'Kelurahan berhasil dihapus.');
    }

    public function bulkDestroy(Request $request)
    {
        $ids = $request->input('selected');

        if ($ids) {
            Kelurahan::whereIn('id', $ids)->delete();
            return redirect()->back()->with('success', 'Data berhasil dihapus.');
        }

        return redirect()->route('location.kelurahan.index')->with('error', 'Tidak ada data yang dipilih.');
    }

}
