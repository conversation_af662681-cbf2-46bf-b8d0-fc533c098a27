<?php

namespace App\Http\Traits;

use App\Models\District;
use App\Models\Job;
use App\Models\Tag;
use App\Models\JobRole;
use App\Models\JobType;
use App\Models\Setting;
use App\Models\Education;
use App\Models\Experience;
use App\Models\JobCategory;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\TagTranslation;
use Illuminate\Support\Facades\DB;
use Modules\Location\Entities\Country;
use Modules\Language\Entities\Language;

trait JobableApi
{
    private function getJobs($request)
    {
        if (auth('sanctum')->user()) {

            $query = Job::with('company.user', 'category', 'job_type:id,name')
                ->withCount([
                    'bookmarkJobs', 'appliedJobs',
                    'bookmarkJobs as bookmarked' => function ($q) {
                        $q->where('candidate_id',  auth('sanctum')->user()->candidate ? auth('sanctum')->user()->candidate->id : '');
                    }, 'appliedJobs as applied' => function ($q) {
                        $q->where('candidate_id',  auth('sanctum')->user()->candidate ? auth('sanctum')->user()->candidate->id : '');
                    }
                ])
                ->active()->withoutEdited();
        } else {

            $query = Job::with('company.user', 'category', 'job_type:id,name')
                ->withCount([
                    'bookmarkJobs', 'appliedJobs',
                    'bookmarkJobs as bookmarked' => function ($q) {
                        $q->where('candidate_id', '');
                    }, 'appliedJobs as applied' => function ($q) {
                        $q->where('candidate_id', '');
                    }
                ])
                ->withoutEdited()
                ->active();
        }

        // company search
        if ($request->has('company') && $request->company != null) {
            $company = $request->company;
            $query->whereHas('company.user', function ($q) use ($company) {
                $q->where('username', $company);
            });
        }

        // Keyword search
        if ($request->has('keyword') && $request->keyword != null) {
            $query->where('title', 'LIKE', "%$request->keyword%");
        }

        // Category filter
        if ($request->has('category') && $request->category != null) {
            $query->where('category_id', $request->category);
        }

        // job role filter
        if ($request->has('job_role') && $request->job_role != null) {
            $query->where('role_id', $request->job_role);
        }

        // Salery filter
        if ($request->has('price_min') && $request->price_min != null) {
            $query->where('min_salary', '>=', $request->price_min);
        }
        if ($request->has('price_max') && $request->price_max != null) {
            $query->where('max_salary', '<=', $request->price_max);
        }

        // tags filter
        if ($request->has('tag') && $request->tag != null) {
            $tag = TagTranslation::where('name', $request->tag)->first();

            if ($tag) {
                $query->whereHas('tags', function ($q) use ($tag) {
                    $q->where('job_tag.tag_id', $tag->tag_id);
                });
            }
        }

        // location (kota)
        if ($request->has('district') && $request->district != null) {
            $district = $request->district;
            $query->where('district', 'LIKE', "%$district%");
        }

        // location
        $final_address = '';
        if ($request->has('location') && $request->location != null) {
            $adress = $request->location;
            if ($adress) {
                $adress_array = explode(" ", $adress);
                if ($adress_array) {
                    $last_two = array_splice($adress_array, -2);
                }
                $final_address = Str::slug(implode(" ", $last_two));
            }
        }
        // lat Long
        if ($request->has('lat') && $request->has('long') && $request->lat != null && $request->long != null) {
            session()->forget('selected_country');
            $ids = $this->location_filter($request);
            $query->whereIn('id', $ids)
                ->orWhere('address', $final_address ? $final_address : '')
                ->orWhere('country', $request->location ? $request->location : '');
        }

        // country
        $selected_country = request()->header('selected_country');

        if ($selected_country && $selected_country != null) {
            $country = $selected_country;
            $query->where('country', 'LIKE', "%$country%");
        } else {

            $setting = Setting::first();
            if ($setting->app_country_type == 'single_base') {
                if ($setting->app_country) {

                    $country = Country::where('id', $setting->app_country)->first();
                    if ($country) {
                        $query->where('country', 'LIKE', "%$country->name%");
                    }
                }
            }
        }

        // Sort by ads
        if ($request->has('sort_by') && $request->sort_by != null) {
            switch ($request->sort_by) {
                case 'latest':
                    $query->latest('id');
                    break;
                case 'featured':
                    $query->where('featured', 1)->latest();
                    break;
            }
        }

        // Experience filter
        if ($request->has('experience') && $request->experience != null) {
            $experience_id = Experience::where('name', $request->experience)->value('id');
            $query->where('experience_id', $experience_id);
        }

        // Education filter
        if ($request->has('education') && $request->education != null) {
            $education_id = Education::where('name', $request->education)->value('id');
            $query->where('education_id', $education_id);
        }

        // Work type filter
        if ($request->has('is_remote') && $request->is_remote != null) {
            $query->where('is_remote', 1);
        }

        // Job type filter
        if ($request->has('job_type') && $request->job_type != null) {
            $job_type_id = JobTypeTranslation::where('name', $request->job_type)->value('job_type_id');
            $query->where('job_type_id', $job_type_id);
        }

        $jobs = $query->latest()->paginate(9)->withQueryString();

        return [
            'total_jobs' => $jobs->total(),
            'jobs' => $jobs,
            'countries' => Country::all(['id', 'name', 'slug']),
            'categories' => JobCategory::all(),
            'job_roles' => JobRole::all(),
            'max_salary' => \DB::table('jobs')->max('max_salary'),
            'min_salary' => \DB::table('jobs')->max('min_salary'),
            'experiences' => Experience::all(),
            'educations' => Education::all(),
            'job_types' => JobType::all(),
            'district' => District::all(),
            // 'locality' => Locality::all(),
            'popularTags' => $this->popularTags(),
        ];
    }

    private function getJobDetails($job)
    {
        if (auth('sanctum')->user()) {

            $job_details = $job->load([
                'bookmarkJobs', 'benefits', 'education',
                'experience', 'tags', 'role',
                'company.user' => function ($q) {
                    return $q->with('contactInfo', 'socialInfo');
                },
                'appliedJobs' => function ($q) {
                    return $q->where('candidate_id',  auth('sanctum')->user()->candidate ? auth('sanctum')->user()->candidate->id : '');
                }
            ])
                ->loadCount([
                    'bookmarkJobs as bookmarked' => function ($q) {
                        $q->where('candidate_id',  auth('sanctum')->user()->candidate ? auth('sanctum')->user()->candidate->id : '');
                    }, 'appliedJobs as applied' => function ($q) {
                        $q->where('candidate_id',  auth('sanctum')->user()->candidate ? auth('sanctum')->user()->candidate->id : '');
                    }
                ]);
        } else {

            $job_details = $job->load([
                'benefits', 'education', 'experience', 'tags', 'role',
                'company.user' => function ($q) {
                    return $q->with('contactInfo', 'socialInfo');
                },
            ])->loadCount([
                'bookmarkJobs', 'appliedJobs',
                'bookmarkJobs as bookmarked' => function ($q) {
                    $q->where('candidate_id',  '');
                }, 'appliedJobs as applied' => function ($q) {
                    $q->where('candidate_id',  '');
                }
            ]);
        }

        // Related Jobs With Single && Multiple Country Base
        if (auth('sanctum')->user()) {
            $related_jobs_query = Job::query()->withoutEdited()->active()->where('id', '!=', $job->id)->where('category_id', $job->category_id);
            $setting = Setting::first();
            if ($setting->app_country_type == 'single_base') {
                if ($setting->app_country) {

                    $country = Country::where('id', $setting->app_country)->first();
                    if ($country) {
                        $related_jobs_query->where('country', 'LIKE', "%$country->name%");
                    }
                }
            } else {
                $selected_country = request()->header('selected_country');

                if ($selected_country && $selected_country != null) {
                    $country = $selected_country;
                    $related_jobs_query->where('country', 'LIKE', "%$country%");
                }
            }
            $related_jobs = $related_jobs_query->latest()->limit(18)
                ->withCount([
                    'bookmarkJobs',
                    'bookmarkJobs as bookmarked' => function ($q) {
                        $q->where('candidate_id',  auth('sanctum')->user()->candidate ? auth('sanctum')->user()->candidate->id : '');
                    }
                ])->get();
        } else {
            $related_jobs_query = Job::query()->withoutEdited()->active()->where('id', '!=', $job->id)->where('category_id', $job->category_id);
            $setting = Setting::first();
            if ($setting->app_country_type == 'single_base') {
                if ($setting->app_country) {

                    $country = Country::where('id', $setting->app_country)->first();
                    if ($country) {
                        $related_jobs_query->where('country', 'LIKE', "%$country->name%");
                    }
                }
            } else {
                $selected_country = request()->header('selected_country');

                if ($selected_country && $selected_country != null) {
                    $country = $selected_country;
                    $related_jobs_query->where('country', 'LIKE', "%$country%");
                }
            }
            $related_jobs = $related_jobs_query->latest()->limit(18)
                ->withCount([
                    'bookmarkJobs',
                    'bookmarkJobs as bookmarked' => function ($q) {
                        $q->where('candidate_id', '');
                    }
                ])->get();
        }

        if (auth('sanctum')->check() && auth('sanctum')->user()->role == 'candidate') {
            $resumes = auth('sanctum')->user()->candidate->resumes;
        } else {
            $resumes = [];
        }

        return [
            'job' => $job_details,
            'related_jobs' => $related_jobs,
            'resumes' => $resumes,
        ];
    }

    public function location_filter($request)
    {
        $latitude = $request->lat;
        $longitude = $request->long;

        if ($request->has('radius') && $request->radius != null) {
            $distance = $request->radius;
        } else {
            $distance = 50;
        }

        $haversine = "(
                    6371 * acos(
                        cos(radians(" . $latitude . "))
                        * cos(radians(`lat`))
                        * cos(radians(`long`) - radians(" . $longitude . "))
                        + sin(radians(" . $latitude . ")) * sin(radians(`lat`))
                    )
                )";

        $data = Job::select('id')->selectRaw("$haversine AS distance")
            ->having("distance", "<=", $distance)->get();

        $ids = [];

        foreach ($data as $id) {
            array_push($ids, $id->id);
        }

        return $ids;
    }

    public function jobTagsInsert($tags, $job)
    {
        if ($tags) {
            $tagsArray = [];

            foreach ($tags as $tag) {
                $taggable = TagTranslation::where('tag_id', $tag)->orWhere('name', $tag)->first();

                if (!$taggable) {
                    $new_tag = Tag::create(['name' => $tag]);

                    $languages = Language::all();
                    foreach ($languages as $language) {
                        $new_tag->translateOrNew($language->code)->name = $tag;
                    }
                    $new_tag->save();

                    array_push($tagsArray, $new_tag->id);
                } else {
                    array_push($tagsArray, $tag);
                }
            }

            $job->tags()->attach($tagsArray);
        }
    }

    public function jobTagsSync($tags, $job)
    {
        if ($tags) {
            $tagsArray = [];

            foreach ($tags as $tag) {
                $taggable = TagTranslation::where('tag_id', $tag)->orWhere('name', $tag)->first();

                if (!$taggable) {
                    $new_tag = Tag::create(['name' => $tag]);

                    $languages = Language::all();
                    foreach ($languages as $language) {
                        $new_tag->translateOrNew($language->code)->name = $tag;
                    }
                    $new_tag->save();

                    array_push($tagsArray, $new_tag->id);
                } else {
                    array_push($tagsArray, $tag);
                }
            }

            $job->tags()->sync($tagsArray);
        }
    }

    public function jobBenefitsInsert($benefits, $job)
    {
        if ($benefits && count($benefits)) {
            $job->benefits()->attach($benefits);
        }
    }

    public function jobBenefitsSync($benefits, $job)
    {
        if ($benefits && count($benefits)) {
            $job->benefits()->sync($benefits);
        }
    }

    public function popularTags()
    {
        return Tag::popular()->withCount('tags')->latest('tags_count')->get()->take(10);
    }
}
