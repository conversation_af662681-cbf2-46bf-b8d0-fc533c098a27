<?php

namespace App\Http\Controllers;

use App\Models\State;
use App\Models\District;
use App\Models\City; // Jika Anda ingin menampilkan kota yang terkait
use Illuminate\Http\Request;

class DistrictController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $keyword = request('keyword');
    
        $districtsQuery = District::query();
    
        // Layer 1: <PERSON>i be<PERSON> keyword
        if ($keyword) {
            $districtsQuery->where('name', 'like', '%' . $keyword . '%');
        }
    
        // Layer 2: Pencarian normal
        if (!$keyword) {
            $districtsQuery->where('id', '>', 0); // A condition that always evaluates to true.
        }
    
        $districts = $districtsQuery->paginate(10);
    
        // Ambil semua kota untuk dropdown
        $cities = City::all();
    
        return view('backend.settings.pages.location.districts.index', compact('districts', 'cities'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $cities = City::all(); // Ambil semua data kota
    
        return view('backend.settings.pages.location.districts.create', compact('cities'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'city_id' => 'required|exists:cities,id', // validasi ID kota
            'new_column' => 'nullable|string', // tambahkan validasi sesuai kebutuhan
        ]);

        District::create($request->all());

        return back()->with('success', 'District berhasil ditambahkan.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $district = District::findOrFail($id);
        $cities = City::all(); // Ambil semua data kota
    
        return view('backend.settings.pages.location.districts.edit', compact('district', 'cities'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $district = District::findOrFail($id);
        $request->validate([
            'name' => 'required|string|max:255',
            'city_id' => 'required|exists:cities,id', // validasi ID kota
            'new_column' => 'nullable|string', // tambahkan validasi sesuai kebutuhan
        ]);

        $district->update($request->all());

        return back()->with('success', 'District berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $district = District::findOrFail($id);
            
            $district->delete();

            return back()->with('success', 'District berhasil dihapus.');
        } catch (\Exception $e) {
            return back()->with('error', 'Gagal menghapus district: ' . $e->getMessage());
        }
    }
}
