<?php

namespace App\Notifications\Admin;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewUserRegisteredNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $admin;

    public $user;

    public function __construct($user, $admin)
    {
        $this->admin = $admin;
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $type = 'new_user_registered';
        $formatted_mail_data = getFormattedTextByType($type, [
            'user_role' => $this->user->role,
            'admin_name' => $this->admin->name,
        ]);
        $subject = $formatted_mail_data['subject'];
        $message = $formatted_mail_data['message'];

        return (new MailMessage)
            ->subject($subject)
            ->line($message)
            ->action('Lihat '.ucfirst($this->user->role), route('user.index'))
            ->view('mails.email-template', ['content' => $message]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        // Tentukan URL berdasarkan role dan ketersediaan relasi
        $url = route('user.index'); // Default URL

        if ($this->user->role == 'company' && $this->user->company) {
            $url = route('company.show', [$this->user->company->id]);
        } elseif ($this->user->role == 'candidate' && $this->user->candidate) {
            $url = route('candidate.show', [$this->user->candidate->id]);
        }

        return [
            'title' => ucfirst($this->user->name).' baru saja mendaftar.',
            'url' => $url,
        ];
    }
}
