<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use App\Services\LogService;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::ADMIN;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest:admin')->except('logout');
    }

    public function showLoginForm()
    {
        if (auth('user')->check()) {
            return redirect()->route('user.dashboard');
        }

        return view('backend.auth.login');
    }

    /**
     * Validate the user login request.
     *
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        $request->validate([
            $this->username() => 'required|string',
            'password' => 'required|string',
            'g-recaptcha-response' => config('captcha.active') ? 'required|captcha' : '',
        ], [
            'g-recaptcha-response.required' => 'Harap verifikasi bahwa Anda bukanlah robot.',
            'g-recaptcha-response.captcha' => 'Captcha error! Coba lagi nanti atau hubungi admin.',
        ]);
    }

    protected function guard()
    {
        return Auth::guard('admin');
    }

    /**
     * The user has been authenticated.
     *k
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        // Record login activity
        LogService::recordLogin($user, 'admin');

        // Force logout from other sessions
        LogService::forceLogoutOtherSessions($user, 'admin');

        // Log admin activity
        LogService::recordAdminActivity('login', 'auth', 'Admin logged in');

        // Use dynamic admin path
        $adminPath = RouteServiceProvider::getAdminPath();
        return redirect()->intended($adminPath);
    }

    /**
     * Logout pengguna dari aplikasi.
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // Define an array of session keys to retain and restore
        $sessionKeys = ['current_currency', 'current_lang', 'selected_country'];

        // Create an array to store the session data
        $sessionData = [];

        // Retain specific session data
        foreach ($sessionKeys as $key) {
            $sessionData[$key] = session($key);
        }

        // Record logout activity if user is logged in
        if ($this->guard()->check()) {
            $user = $this->guard()->user();
            LogService::recordLogout($user, 'admin');
            LogService::recordAdminActivity('logout', 'auth', 'Admin logged out');
        }

        $this->guard()->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        // Restore specific session data
        foreach ($sessionData as $key => $value) {
            session([$key => $value]);
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect()->route('admin.login');
    }
}
