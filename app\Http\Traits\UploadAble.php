<?php

namespace App\Http\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

trait UploadAble
{

    /**
     * Upload one file.
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string|null $folder
     * @param string $disk
     * @param string|null $filename
     * @return string
     */
    public function uploadOne(UploadedFile $file, $folder = null, $disk = 'public', $filename = null)
    {
        $name = ! is_null($filename) ? $filename : uniqid('FILE_').dechex(time());

        return $file->storeAs(
            $folder,
            $name.'.'.$file->getClientOriginalExtension(),
            $disk
        );
    }

    /**
     * Delete a file from the specified disk.
     *
     * @param string|null $path The path to the file to be deleted.
     * @param string $disk The name of the disk where the file is stored (default is 'public').
     * @return void
     */
    public function deleteOne($path = null, $disk = 'public')
    {
        Storage::disk($disk)->delete($path);
    }
}
