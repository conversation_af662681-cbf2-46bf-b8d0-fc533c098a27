<?php

namespace App\Export;

use App\Models\Candidate;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;

class CandidateExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle, ShouldAutoSize
{
    public function collection()
    {
        $candidates = Candidate::with(['user', 'education', 'profession'])
            ->orderBy('created_at', 'desc')
            ->get();

        if ($candidates->isEmpty()) {
            return collect(); // Return empty
        }

        return $candidates->map(function ($candidate) {
            return [
                'nik' => $candidate->user->nik ?? '-',
                'nama' => $candidate->user->name ?? '-',
                'email' => $candidate->user->email ?? '-',
                'jenis_kelamin' => $candidate->gender == 'male' ? 'Laki-laki' : 'Perempuan',
                'no_hp' => $candidate->user->no_hp ?? '-',
                'pendidikan' => $candidate->education ? $candidate->education->name : '-',
                'profesi' => $candidate->profession ? $candidate->profession->name : '-',
                'alamat' => $candidate->user->alamat_ktp ?? '-',
                'kecamatan' => $candidate->user->kecamatan ?? '-',
                'kabupaten_kota' => $candidate->user->kabupaten_kota ?? '-',
                'provinsi' => $candidate->user->provinsi ?? '-',
                'status' => $candidate->user->status == 1 ? 'Aktif' : 'Tidak Aktif',
                'tanggal_daftar' => $candidate->created_at ? $candidate->created_at->format('d/m/Y') : '-',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'NIK',
            'Nama',
            'Email',
            'Jenis Kelamin',
            'No HP',
            'Pendidikan',
            'Profesi',
            'Alamat',
            'Kecamatan',
            'Kabupaten/Kota',
            'Provinsi',
            'Status',
            'Tanggal Daftar'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20, // NIK
            'B' => 30, // Nama
            'C' => 30, // Email
            'D' => 15, // Jenis Kelamin
            'E' => 15, // No HP
            'F' => 20, // Pendidikan
            'G' => 20, // Profesi
            'H' => 40, // Alamat
            'I' => 20, // Kecamatan
            'J' => 20, // Kabupaten/Kota
            'K' => 20, // Provinsi
            'L' => 15, // Status
            'M' => 15, // Tanggal Daftar
        ];
    }

    public function title(): string
    {
        return 'Daftar Pencaker';
    }

    public function styles($sheet)
    {
        $lastColumn = 'M'; // Sesuaikan dengan jumlah kolom
        $lastRow = $sheet->getHighestRow();

        // Style untuk header
        $headerRange = 'A1:' . $lastColumn . '1';
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '007BFF'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Style untuk data
        $dataRange = 'A2:' . $lastColumn . $lastRow;
        $sheet->getStyle($dataRange)->applyFromArray([
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Zebra striping untuk baris
        for ($row = 2; $row <= $lastRow; $row++) {
            if ($row % 2 == 0) {
                $sheet->getStyle('A' . $row . ':' . $lastColumn . $row)->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => 'F2F2F2'],
                    ],
                ]);
            }
        }

        // Menyesuaikan tinggi baris header
        $sheet->getRowDimension(1)->setRowHeight(25);

        // Menyesuaikan tinggi baris data
        for ($row = 2; $row <= $lastRow; $row++) {
            $sheet->getRowDimension($row)->setRowHeight(20);
        }

        // Mengaktifkan wrap text untuk kolom alamat
        $sheet->getStyle('H2:H' . $lastRow)->getAlignment()->setWrapText(true);
    }
}
