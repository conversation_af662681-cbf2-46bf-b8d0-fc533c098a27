<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\SearchCountry;
use App\Models\State;
use App\Models\City;
use App\Models\Kecamatan;
use App\Models\Kelurahan;

class RegisterLocationSelector extends Component
{
    public $countries = [];
    public $states = [];
    public $cities = [];
    public $kecamatans = [];
    public $kelurahans = [];

    public $row = false;
    public $selectedCountryId = 'Indonesia'; // Set default country
    public $selectedStateId = 'Banten'; // Set default province
    public $selectedCityId = 'Tangerang Selatan'; // Set default city
    public $selectedKecamatanId;
    public $selectedKelurahanId;

    public $userType = 'candidate'; // Default to candidate

    protected $listeners = [
        'getStateByCountryId' => 'getStateByCountryId',
        'getCityByStateId' => 'getCityByStateId',
        'getKecamatanByCityId' => 'getKecamatanByCityId',
        'getKelurahanByKecamatanId' => 'getKelurahanByKecamatanId',
        'userTypeChanged' => 'userTypeChanged',
    ];

    public function hydrate()
    {
        $this->dispatchBrowserEvent('render-select2');
    }

    public function render()
    {
        // Load countries
        $this->countries = SearchCountry::select('id', 'name')
            ->where('name', 'Indonesia')
            ->get()
            ->toArray();

        // Load states (provinces) - show all states for both candidates and companies
        $this->states = State::select('id', 'name', 'country_id')
            ->get()
            ->toArray();

        // Load cities if state (province) is selected
        if ($this->selectedStateId) {
            $selectedState = State::where('name', $this->selectedStateId)->orWhere('id', $this->selectedStateId)->first();
            if ($selectedState) {
                $stateId = $selectedState->id;

                // Show all cities for both candidates and companies
                $this->cities = City::select('id', 'name', 'state_id')
                    ->where('state_id', $stateId)
                    ->get()
                    ->toArray();
            } else {
                $this->cities = [];
            }
        } else {
            $this->cities = [];
        }

        // Load kecamatans (districts) if city is selected
        if ($this->selectedCityId) {
            $selectedCity = City::where('name', $this->selectedCityId)->first();
            if ($selectedCity) {
                $cityId = $selectedCity->id;
                $this->kecamatans = Kecamatan::select('id', 'name', 'city_id')
                    ->where('city_id', $cityId)
                    ->get()
                    ->toArray();
            } else {
                $this->kecamatans = [];
            }
        } else {
            $this->kecamatans = [];
        }

        // Load kelurahans (villages) if kecamatan is selected
        if ($this->selectedKecamatanId) {
            $selectedKecamatan = Kecamatan::where('name', $this->selectedKecamatanId)->first();
            if ($selectedKecamatan) {
                $kecamatanId = $selectedKecamatan->id;
                $this->kelurahans = Kelurahan::select('id', 'name', 'kecamatan_id')
                    ->where('kecamatan_id', $kecamatanId)
                    ->get()
                    ->toArray();
            } else {
                $this->kelurahans = [];
            }
        } else {
            $this->kelurahans = [];
        }

        return view('livewire.register-location-selector');
    }

    public function userTypeChanged($type)
    {
        $this->userType = $type;

        // Reset selections for both candidates and companies
        $this->selectedStateId = null;
        $this->selectedCityId = null;
        $this->selectedKecamatanId = null;
        $this->selectedKelurahanId = null;
    }

    public function getStateByCountryId()
    {
        // Show all states for both candidates and companies
        $this->states = State::select('id', 'name', 'country_id')
            ->get()
            ->toArray();
    }

    public function getCityByStateId()
    {
        $selectedState = State::where('name', $this->selectedStateId)->orWhere('id', $this->selectedStateId)->first();
        if ($selectedState) {
            $stateId = $selectedState->id;

            if ($this->userType === 'candidate' && ($this->selectedStateId === 'Banten' || $selectedState->name === 'Banten')) {
                // For candidates, only show Tangerang Selatan in Banten
                $this->cities = City::select('id', 'name', 'state_id')
                    ->where('state_id', $stateId)
                    ->where('name', 'Tangerang Selatan')
                    ->get()
                    ->toArray();
            } else {
                // For companies or other states, show all cities
                $this->cities = City::select('id', 'name', 'state_id')
                    ->where('state_id', $stateId)
                    ->get()
                    ->toArray();
            }
        } else {
            $this->cities = [];
        }

        // Reset dependent fields
        $this->selectedKecamatanId = null;
        $this->selectedKelurahanId = null;
        $this->kecamatans = [];
        $this->kelurahans = [];
    }

    public function getKecamatanByCityId()
    {
        $selectedCity = City::where('name', $this->selectedCityId)->orWhere('id', $this->selectedCityId)->first();
        if ($selectedCity) {
            $cityId = $selectedCity->id;
            $this->kecamatans = Kecamatan::select('id', 'name', 'city_id')
                ->where('city_id', $cityId)
                ->get()
                ->toArray();
        } else {
            $this->kecamatans = [];
        }

        // Reset dependent fields
        $this->selectedKelurahanId = null;
        $this->kelurahans = [];
    }

    public function getKelurahanByKecamatanId()
    {
        $selectedKecamatan = Kecamatan::where('name', $this->selectedKecamatanId)->orWhere('id', $this->selectedKecamatanId)->first();
        if ($selectedKecamatan) {
            $kecamatanId = $selectedKecamatan->id;
            $this->kelurahans = Kelurahan::select('id', 'name', 'kecamatan_id')
                ->where('kecamatan_id', $kecamatanId)
                ->get()
                ->toArray();
        } else {
            $this->kelurahans = [];
        }
    }

    public function updatedSelectedStateId($value)
    {
        $this->selectedStateId = $value;
        $this->getCityByStateId();
    }

    public function updatedSelectedCityId($value)
    {
        $this->selectedCityId = $value;
        $this->getKecamatanByCityId();
    }

    public function updatedSelectedKecamatanId($value)
    {
        $this->selectedKecamatanId = $value;
        $this->getKelurahanByKecamatanId();
    }
}
