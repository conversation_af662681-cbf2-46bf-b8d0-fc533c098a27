<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Traits\HasCountryBasedJobs;
use App\Mail\SendCandidateMail;
use App\Models\Admin;
use App\Models\Candidate;
use App\Models\EmailTemplate;
use App\Models\User;
use App\Notifications\Admin\NewUserRegisteredNotification;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use HasCountryBasedJobs, RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Show the application registration form.
     *
     * @return \Illuminate\View\View
     */
    public function showRegistrationForm()
    {
        $data['candidates'] = Candidate::count();

        return view('frontend.auth.register', $data);
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        $rules = [
                'name' => ['required', 'string', 'regex:/^[\pL\s.,]+$/u', 'max:100'],
                'nik' => ['required', 'numeric', 'digits_between:13,16'],
                'provinsi' => ['required', 'string'],
                'kabupaten_kota' => ['required', 'string'],
                'kecamatan' => ['required', 'string'],
                'kelurahan' => ['nullable', 'string'],
                'tempat_lahir' => ['nullable', 'string', 'regex:/^[\pL\s]+$/u'],
                'tanggal_lahir' => ['nullable', 'date'],
                'jenis_kelamin' => ['nullable', 'string'],
                'status_perkawinan' => ['nullable', 'string'],
                'agama' => ['nullable', 'string'],
                'pendidikan' => ['nullable', 'string'],
                'no_hp' => ['required', 'regex:/^(08|628)[0-9]{8,14}$/'],
                'ak1' => $data['role'] === 'candidate' ? ['required', 'file', 'mimes:jpeg,png,jpg,pdf', 'max:10240'] : ['nullable'],
                'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
                'password' => ['required', 'string', 'min:6', 'confirmed'],
                'g-recaptcha-response' => config('captcha.active') ? 'required|captcha' : 'nullable',
            ];

        // Tambahkan validasi untuk field perusahaan jika role adalah company
        if (isset($data['role']) && $data['role'] == 'company') {
            $rules['nama_hrd'] = ['required', 'string', 'max:100'];
            $rules['jabatan'] = ['required', 'string', 'max:100'];
        }

        return Validator::make(
            $data,
            $rules,
            [
                'name.regex' => 'Nama hanya boleh mengandung huruf, spasi, titik, dan koma.',
                'nik.digits_between' => 'NIK harus berisi antara 13 hingga 16 digit.',
                'tempat_lahir.regex' => 'Tempat lahir hanya boleh mengandung huruf dan spasi.',
                'no_hp.regex' => 'Nomor telepon harus dimulai dengan 08 atau 628 dan memiliki panjang 8 hingga 14 digit.',
                'ak1.required' => 'Field AK1 wajib diisi.',
                'ak1.mimes' => 'AK1 harus berupa file dengan format: jpeg, png, jpg, atau pdf.',
                'ak1.max' => 'Ukuran file AK1 tidak boleh lebih dari 10 MB.',
                'nama_hrd.required' => 'Nama penanggung jawab wajib diisi.',
                'nama_hrd.max' => 'Nama penanggung jawab maksimal 100 karakter.',
                'jabatan.required' => 'Jabatan wajib diisi.',
                'jabatan.max' => 'Jabatan maksimal 100 karakter.',
                'g-recaptcha-response.required' => 'Mohon verifikasi bahwa Anda bukan robot.',
                'g-recaptcha-response.captcha' => 'Captcha error! Coba lagi nanti atau hubungi admin situs.',
            ]
        );
    }

    // public function validateField(Request $request)
    // {
    //     $validator = Validator::make($request->all(), [
    //         'name' => ['required', 'string', 'regex:/^[\pL\s]+$/u', 'max:100'],
    //         // Define other rules as needed
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json(['errors' => $validator->errors()], 422);
    //     }

    //     return response()->json(['success' => true]);
    // }


    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        // Generate username
        $newUsername = Str::slug($data['name']);
        $oldUserName = User::where('username', $newUsername)->first();

        if ($oldUserName) {
            $username = Str::slug($newUsername) . '_' . Str::random(5);
        } else {
            $username = Str::slug($newUsername);
        }

        $ak1Path = null;

        // Process upload file AK1 if role is candidate or company and file AK1 exists
        if (isset($data['ak1']) && ($data['role'] === 'candidate' || $data['role'] === 'company')) {
            $ak1Path = $data['ak1']->store('uploads/files/users/ak1', 'public');
        }

        $template = EmailTemplate::where('type', $data['role'] == 'candidate' ? 'new_candidate' : 'new_company')->first();

        if ($template) {
            Mail::to($data['email'])->send(new SendCandidateMail($username, $template->subject, $template->message));
        }

        // Prepare data to be saved in users table
        $userData = [
            'role' => $data['role'] == 'candidate' ? 'candidate' : 'company',
            'name' => $data['role'] == 'company' && isset($data['full_company_name']) && !empty($data['full_company_name']) ? $data['full_company_name'] : $data['name'],
            'username' => $username,
            'nik' => $data['nik'],
            'provinsi' => $data['provinsi'],
            'kabupaten_kota' => $data['kabupaten_kota'],
            'kecamatan' => $data['kecamatan'],
            'kelurahan' => $data['kelurahan'],
            'alamat_ktp' => $data['alamat_ktp'],
            'tempat_lahir' => $data['tempat_lahir'],
            'tanggal_lahir' => $data['tanggal_lahir'],
            'jenis_kelamin' => $data['jenis_kelamin'],
            'status_perkawinan' => $data['status_perkawinan'],
            'agama' => $data['agama'],
            'pendidikan_terakhir' => $data['pendidikan_terakhir'],
            'no_hp' => $data['no_hp'],
            'ak1' => $ak1Path, // Include AK1 file path or null if empty
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ];

        // Tambahkan data nama_hrd dan jabatan jika role adalah company
        if ($data['role'] == 'company') {
            if (isset($data['nama_hrd'])) {
                $userData['nama_hrd'] = $data['nama_hrd'];
            }
            if (isset($data['jabatan'])) {
                $userData['jabatan'] = $data['jabatan'];
            }
        }

        // Create the user
        $user = User::create($userData);

        // Status user akan diatur otomatis oleh model User berdasarkan setting aktivasi otomatis

        // Sync data into candidates table if role is 'candidate'
        if ($user->role == 'candidate') {
            Candidate::updateOrCreate(
                ['user_id' => $user->id], // Search by user_id
                [
                    'user_id' => $user->id,
                    'gender' => $data['jenis_kelamin'] === 'Laki-laki' ? 'male' : 'female',
                    'marital_status' => $data['status_perkawinan'],
                    'birth_date' => $data['tanggal_lahir'],
                    'exact_location' => $data['alamat_ktp'],
                    'district' => $data['kabupaten_kota'],
                    'region' => $data['provinsi'],
                    'whatsapp_number' => $data['no_hp'],
                    'ktp_image' => $ak1Path,
                ]
            );
        }

        // Notify admins about new user registration (setelah relasi dibuat)
        $admins = Admin::all();
        foreach ($admins as $admin) {
            Notification::send($admin, new NewUserRegisteredNotification($user, $admin));
        }

        return $user;
    }

    /**
     * Handle the registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function register(Request $request)
    {
        $this->validator($request->all())->validate();
        $user = $this->create($request->all());

        // Login pengguna setelah pendaftaran
        Auth::login($user);

        // Arahkan ke halaman dasbor
        return redirect()->route('user.dashboard');
    }

    /**
     * Proses upload file AK1 saat registrasi.
     */
    public function uploadFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:10240',
            'ak1' => $request->input('role') === 'candidate' ? 'required|file|mimes:jpeg,png,jpg,pdf|max:10240' : 'nullable',
        ]);

        // Mengambil file yang diupload
        $file = $request->file('file');
        $originalFilename = $file->getClientOriginalName();
        $destinationPath = public_path('uploads/file/candidates');

        // Memastikan folder tujuan ada, jika tidak ada maka dibuat
        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0777, true);
        }

        // Menyimpan file ke lokasi yang diinginkan
        $file->move($destinationPath, $originalFilename);

        // Path untuk disimpan di database atau digunakan lebih lanjut
        $filePath = 'uploads/file/candidates/' . $originalFilename;

        return response()->json(['filepath' => $filePath]);
    }
}
