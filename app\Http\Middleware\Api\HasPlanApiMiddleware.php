<?php

namespace App\Http\Middleware\Api;

use App\Models\Earning;
use Modules\Plan\Entities\Plan;
use App\Models\UserPlan;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HasPlanApiMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth('sanctum')->check() && auth('sanctum')->user() && auth('sanctum')->user()->role == 'company') {

            $user  =  Auth::user();
            $company = $user->company;
            $plan = $company->userPlan;

            // Jika perusahaan tidak memiliki plan, tetapkan plan default secara otomatis
            if (!$plan) {
                $this->assignDefaultPlan($company);

                // Cek apakah ada pending order
                $check_pending_plan = $this->checkPendingPlan($company);
                if ($check_pending_plan) {
                    abort(401, __('your_purchased_plan_order_has_pending._please_wait_until_the_order_is_approved'));
                } else {
                    abort(401, __('a_default_plan_has_been_assigned_to_your_company'));
                }
            } else {
                return $next($request);
            }
        }
        return $next($request);
    }

    /**
     * Assign the default plan to the company
     *
     * @param  object  $company
     * @return void
     */
    private function assignDefaultPlan(object $company): void
    {
        // Dapatkan plan default atau yang pertama tersedia
        $plan = Plan::findOrFail(1);

        // Buat UserPlan baru jika belum ada
        if (!$company->userPlan) {
            $userPlan = new UserPlan();
            $userPlan->company_id = $company->id;
            $userPlan->plan_id = $plan->id;
            $userPlan->job_limit = 1000; // Memberikan 1000 job posting
            $userPlan->featured_job_limit = $plan->featured_job_limit;
            $userPlan->highlight_job_limit = $plan->highlight_job_limit;
            $userPlan->candidate_cv_view_limit = $plan->candidate_cv_view_limit;
            $userPlan->candidate_cv_view_limitation = 'unlimited'; // Memberikan akses unlimited untuk melihat CV
            $userPlan->save();
        }
    }


    /**
     * Cek apakah perusahaan memiliki pending plan
     *
     * @param  object  $company
     * @return bool
     */
    public function checkPendingPlan(object $company): bool
    {
        $earnings = Earning::where('company_id', $company->id)
            ->where('payment_status', 'unpaid')
            ->first();

        return (bool) $earnings;
    }
}
