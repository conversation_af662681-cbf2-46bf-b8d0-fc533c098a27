<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Job;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class JobDataTableController extends Controller
{
    /**
     * Handle DataTables AJAX request for jobs
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        abort_if(!userCan('job.view'), 403);

        $jobs = Job::query()
            ->with([
                'company.user:id,name',
                'category.translations',
                'job_type.translations',
                'experience.translations'
            ])
            ->withCount('appliedJobs')
            ->select([
                'jobs.id',
                'jobs.title',
                'jobs.company_id',
                'jobs.category_id',
                'jobs.job_type_id',
                'jobs.experience_id',
                'jobs.salary_mode',
                'jobs.min_salary',
                'jobs.max_salary',
                'jobs.custom_salary',
                'jobs.deadline',
                'jobs.status',
                'jobs.featured',
                'jobs.highlight',
                'jobs.is_remote'
            ]);

        // Filter by category
        if ($request->has('category') && $request->category != '') {
            $jobs->where('jobs.category_id', $request->category);
        }

        // Filter by experience
        if ($request->has('experience') && $request->experience != '') {
            $jobs->where('jobs.experience_id', $request->experience);
        }

        // Filter by job type
        if ($request->has('job_type') && $request->job_type != '') {
            $jobs->where('jobs.job_type_id', $request->job_type);
        }

        // Filter by company
        if ($request->has('company') && $request->company != '') {
            $jobs->where('jobs.company_id', $request->company);
        }

        // Filter by status
        if ($request->has('status') && $request->status != '') {
            $jobs->where('jobs.status', $request->status);
        }

        // Sort by salary
        if ($request->has('sort_salary') && $request->sort_salary != '') {
            if ($request->sort_salary == 'highest') {
                $jobs->orderBy('jobs.max_salary', 'desc');
            } elseif ($request->sort_salary == 'lowest') {
                $jobs->orderBy('jobs.min_salary', 'asc');
            }
        }

        // Sort by date
        if ($request->has('sort_by') && $request->sort_by != '') {
            if ($request->sort_by == 'oldest') {
                $jobs->orderBy('jobs.created_at', 'asc');
            } else {
                $jobs->orderBy('jobs.created_at', 'desc');
            }
        } else {
            $jobs->orderBy('jobs.created_at', 'desc'); // Default sorting by latest
        }

        // Filter by search term
        if ($request->has('search') && $request->search['value'] != '') {
            $searchValue = $request->search['value'];
            $jobs->where(function ($query) use ($searchValue) {
                $query->where('jobs.title', 'like', "%{$searchValue}%")
                    ->orWhere('jobs.salary_mode', 'like', "%{$searchValue}%")
                    ->orWhere('jobs.min_salary', 'like', "%{$searchValue}%")
                    ->orWhere('jobs.max_salary', 'like', "%{$searchValue}%")
                    ->orWhereHas('company.user', function ($q) use ($searchValue) {
                        $q->where('users.name', 'like', "%{$searchValue}%");
                    })
                    ->orWhereHas('category.translations', function ($q) use ($searchValue) {
                        $q->where('job_category_translations.name', 'like', "%{$searchValue}%");
                    })
                    ->orWhereHas('job_type.translations', function ($q) use ($searchValue) {
                        $q->where('job_type_translations.name', 'like', "%{$searchValue}%");
                    });
            });
        }

        return DataTables::of($jobs)
            ->addIndexColumn()
            ->editColumn('checkbox', function ($row) {
                return '<input type="checkbox" name="job_id[]" value="' . $row->id . '">';
            })
            ->editColumn('title', function ($row) {
                $title = '<a href="' . route('job.show', $row->id) . '">' . $row->title . '</a>';
                $badge = '';

                if ($row->featured) {
                    $badge .= '<span class="badge badge-warning ml-1">Featured</span>';
                }
                if ($row->highlight) {
                    $badge .= '<span class="badge badge-info ml-1">Highlight</span>';
                }
                if ($row->is_remote) {
                    $badge .= '<span class="badge badge-success ml-1">Remote</span>';
                }

                return $title . ' ' . $badge;
            })
            ->editColumn('category', function ($row) {
                return $row->category && isset($row->category->translations[0]) ? $row->category->translations[0]->name : '';
            })
            ->editColumn('salary', function ($row) {
                if ($row->salary_mode == 'range') {
                    return currencyAmountShort($row->min_salary) . ' - ' . currencyAmountShort($row->max_salary);
                } else {
                    return $row->custom_salary ?? '';
                }
            })
            ->editColumn('deadline', function ($row) {
                if (!$row->deadline) return '';

                // Set locale to Indonesian
                setlocale(LC_TIME, 'id_ID.utf8', 'id_ID', 'id');

                // Format date in Indonesian
                $months = [
                    'January' => 'Januari',
                    'February' => 'Februari',
                    'March' => 'Maret',
                    'April' => 'April',
                    'May' => 'Mei',
                    'June' => 'Juni',
                    'July' => 'Juli',
                    'August' => 'Agustus',
                    'September' => 'September',
                    'October' => 'Oktober',
                    'November' => 'November',
                    'December' => 'Desember'
                ];

                $date = date('j F Y', strtotime($row->deadline));

                // Replace English month with Indonesian month
                foreach ($months as $en => $id) {
                    $date = str_replace($en, $id, $date);
                }

                return $date;
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'active') {
                    return '<span class="badge badge-success">Active</span>';
                } elseif ($row->status == 'pending') {
                    return '<span class="badge badge-warning">Pending</span>';
                } elseif ($row->status == 'expired') {
                    return '<span class="badge badge-danger">Expired</span>';
                } else {
                    return '<span class="badge badge-secondary">' . ucfirst($row->status) . '</span>';
                }
            })
            ->editColumn('applications', function ($row) {
                return '<div class="d-flex justify-content-center align-items-center">
                            <i class="fas fa-user mr-1"></i>
                            <a href="' . route('admin.job.applications', $row->id) . '">' . $row->applied_jobs_count . '</a>
                        </div>';
            })
            ->addColumn('company', function ($row) {
                if ($row->company && $row->company->user) {
                    return '<a href="' . route('company.show', $row->company_id) . '">' . formatCompanyName($row->company) . '</a>';
                } elseif ($row->company_name) {
                    return $row->company_name;
                } else {
                    return '';
                }
            })
            ->addColumn('action', function ($row) {
                $actionBtn = '<div class="d-flex">';

                // View detail button with eye icon
                $actionBtn .= '<button type="button" onclick="viewJobDetail(' . $row->id . ')" class="btn btn-sm btn-info mr-1" data-toggle="tooltip" title="Lihat Detail"><i class="fas fa-eye"></i></button>';

                if (userCan('job.update')) {
                    // Edit button
                    $actionBtn .= '<a href="' . route('job.edit', $row->id) . '" class="btn btn-sm btn-primary mr-1" data-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>';

                    // Status buttons
                    if ($row->status == 'pending') {
                        $actionBtn .= '<button type="button" onclick="changeStatus(' . $row->id . ', \'active\')" class="btn btn-sm btn-success mr-1" data-toggle="tooltip" title="Terbitkan"><i class="fas fa-check"></i></button>';
                    } elseif ($row->status == 'active') {
                        $actionBtn .= '<button type="button" onclick="changeStatus(' . $row->id . ', \'pending\')" class="btn btn-sm btn-warning mr-1" data-toggle="tooltip" title="Tunda"><i class="fas fa-pause"></i></button>';
                    } elseif ($row->status == 'expired') {
                        $actionBtn .= '<button type="button" onclick="changeStatus(' . $row->id . ', \'active\')" class="btn btn-sm btn-info mr-1" data-toggle="tooltip" title="Aktifkan Kembali"><i class="fas fa-redo"></i></button>';
                    }
                }

                if (userCan('job.delete')) {
                    $actionBtn .= '<form action="' . route('job.destroy', $row->id) . '" method="POST" class="d-inline">
                        ' . method_field('DELETE') . csrf_field() . '
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure you want to delete this item?\')" data-toggle="tooltip" title="Hapus"><i class="fas fa-trash"></i></button>
                    </form>';
                }

                $actionBtn .= '</div>';

                return $actionBtn;
            })
            ->rawColumns(['checkbox', 'title', 'status', 'applications', 'company', 'action'])
            ->make(true);
    }
}
