<?php

namespace App\Http\Controllers;

use App\Models\Kecamatan;
use App\Models\City;
use Illuminate\Http\Request;

class KecamatanController extends Controller
{
    /**
     * Menampilkan halaman data kecamatan
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $cities = City::all();

        $query = Kecamatan::query();

        if ($request->has('keyword')) {
            $query->where('name', 'like', '%' . $request->keyword . '%');
        }

        if ($request->has('city')) {
            $query->where('city_id', $request->city);
        }

        // Ambil data kecamatan dengan pagination
        $kecamatan = $query->paginate(10);

        return view('backend.settings.pages.location.kecamatan.index', [
            'kecamatan' => $kecamatan,
            'cities' => $cities, // Kirim data kota ke tampilan
        ]);
    }

    /**
     * Menampilkan form untuk membuat kecamatan baru
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $cities = City::all(); // Mengambil semua kota untuk dropdown
        return view('backend.settings.pages.location.kecamatan.create', compact('cities'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'city_id' => 'required|exists:cities,id',
            // Hapus validasi untuk lat dan long
        ]);

        Kecamatan::create($request->all());

        return redirect()->route('location.kecamatan.create')->with('success', __('Kecamatan berhasil ditambahkan.'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Kecamatan  $kecamatan
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Kecamatan $kecamatan)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'city_id' => 'required|exists:cities,id',
        ]);

        $kecamatan->update($request->all());

        return redirect()->route('location.kecamatan.edit')->with('success', __('Kecamatan berhasil diperbarui.'));
    }

    /**
     * Menampilkan form edit untuk kecamatan tertentu.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Kecamatan  $kecamatan
     * @return \Illuminate\View\View
     *
     * Validasi input latitude dan longitude sebagai opsional dan harus berupa angka jika diisi.
     * Mengambil semua kota untuk dropdown dan menampilkan tampilan edit kecamatan.
     */
    public function edit(Request $request, Kecamatan $kecamatan)
    {
        $request->validate([
            'lat' => 'nullable|numeric',  // Mengizinkan lat sebagai opsional dan harus berupa angka jika diisi
            'long' => 'nullable|numeric', // Mengizinkan long sebagai opsional dan harus berupa angka jika diisi
        ]);

        $cities = City::all(); // Mengambil semua kota untuk dropdown
        return view('backend.settings.pages.location.kecamatan.edit', compact('kecamatan', 'cities'));
    }

    /**
     * Menghapus kecamatan.
     *
     * @param  \App\Models\Kecamatan  $kecamatan
     * @return \Illuminate\Http\Response
     */
    public function destroy(Kecamatan $kecamatan)
    {
        // Hapus kecamatan
        $kecamatan->delete();

        return redirect()->route('location.kecamatan.index')->with('success', __('Kecamatan berhasil dihapus.'));
    }
}
