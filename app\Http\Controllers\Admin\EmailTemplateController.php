<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\AdminEmailTemplateRequest;
use App\Models\EmailTemplate;

class EmailTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        try {
            $email_templates = EmailTemplate::all();

            return view('backend.settings.pages.email-template', compact('email_templates'));
        } catch (\Exception $e) {
            flashError('<PERSON><PERSON><PERSON><PERSON>: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Models\EmailTemplate  $emailTemplate
     * @return \Illuminate\Http\Response
     */
    public function save(AdminEmailTemplateRequest $request)
    {
        try {
            $email_template_data = [
                'subject' => $request->subject,
                'message' => $request->message,
            ];

            if (! empty($email_template_data['name'])) {
                $email_template_data['name'] = $request->name;
            }

            $email_template = ! empty($request->id) ? EmailTemplate::find($request->id) : null;

            if ($email_template) {
                $email_template = $email_template->update($email_template_data);
            } else {
                $email_template_data['type'] = $request->type;
                $email_template = EmailTemplate::create($email_template_data);
            }

            if ($email_template) {
                return back()->with('success', __('Template email disimpan!'));
            }

            return back()->with('error', __('Gagal menyimpan template email!'));
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * format `$message` by `$type`
     *
     * @param  string  $message
     * @return mixed formatted data
     */
    public static function getFormattedTextByType(string $type, $data = null)
    {
        try {
            $type_data = self::getDataByType($type, $data);
            $formatter = self::getFormatterByType($type, $type_data);
            $email_template = EmailTemplate::where('type', $type)->first();
            $subject = optional($email_template)->subject ?? '';
            $message = optional($email_template)->message ?? '';

            // Pastikan formatter memiliki search dan replace arrays
            $search = $formatter['search'] ?? [];
            $replace = $formatter['replace'] ?? [];

            // Jika tidak ada data, ganti semua placeholder dengan string kosong
            if (empty($replace) && !empty($search)) {
                $replace = array_fill(0, count($search), '');
            }

            return [
                'subject' => html_entity_decode(str_replace($search, $replace, $subject)),
                'message' => html_entity_decode(str_replace($search, $replace, $message)),
            ];
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return [
                'subject' => '',
                'message' => ''
            ];
        }
    }

    /**
     * dapatkan data berdasarkan tipe yang akan diganti dengan bendera
     *
     * @param  string  $type  jenis template email
     * @param  mixed  $data  data apa pun yang dilewatkan
     * @return array
     */
    public static function getDataByType($type, $data = null)
    {
        try {
            $return_data = [];

            // if the type is among the following, key and value is the same
            $supported_types = [
                'new_user', 'new_edited_job_available', 'new_job_available', 'new_plan_purchase',
                'new_user_registered', 'plan_purchase', 'new_pending_candidate',
                'new_candidate', 'new_company_pending', 'new_company',
                'update_company_pass', 'verify_subscription_notification',
                'candidate_accepted', 'candidate_rejected', 'interview_invitation',
                'reset_password', 'email_verification', 'new_job_notification',
                'flyer_approved', 'flyer_rejected', 'new_message'
            ];

            if (in_array($type, $supported_types)) {
                $return_data = $data ?? [];
            }

            return $return_data;
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * format bendera dengan data
     *
     * @param  mixed  $data
     * @return array array dengan pencarian dan penggantian data
     */
    public static function getFormatterByType(string $type, $data = null)
    {
        try {
            $format = [];

            if ($type === 'new_user') {
                $format['search'] = ['{user_name}', '{company_name}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['company_name'] ?? config('app.name', '')
                    ];
                }
            }

            if ($type === 'new_edited_job_available') {
                $format['search'] = ['{admin_name}'];

                if ($data !== null) {
                    $format['replace'] = [$data['admin_name'] ?? ''];
                }
            }

            if ($type === 'new_job_available') {
                $format['search'] = ['{admin_name}'];

                if ($data !== null) {
                    $format['replace'] = [$data['admin_name'] ?? ''];
                }
            }

            if ($type === 'new_plan_purchase') {
                $format['search'] = ['{admin_name}', '{user_name}', '{plan_label}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['admin_name'] ?? '',
                        $data['user_name'] ?? '',
                        $data['plan_label'] ?? ''
                    ];
                }
            }

            if ($type === 'new_user_registered') {
                $format['search'] = ['{admin_name}', '{user_role}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['admin_name'] ?? '',
                        $data['user_role'] ?? ''
                    ];
                }
            }

            if ($type === 'plan_purchase') {
                $format['search'] = ['{user_name}', '{plan_type}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['plan_type'] ?? ''
                    ];
                }
            }

            // pengguna pelamar dan perusahaan
            if (in_array($type, ['new_pending_candidate', 'new_candidate', 'new_company_pending', 'new_company'])) {
                $format['search'] = ['{user_name}', '{user_email}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['user_email'] ?? ''
                    ];
                }
            }

            if ($type === 'update_company_pass') {
                $format['search'] = ['{user_name}', '{user_email}', '{account_type}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['user_email'] ?? '',
                        $data['account_type'] ?? ''
                    ];
                }
            }

            if ($type === 'verify_subscription_notification') {
                $format['search'] = ['{verify_subscription}'];

                if ($data !== null) {
                    $format['replace'] = [$data['verify_subscription'] ?? ''];
                }
            }

            // Template baru
            if ($type === 'candidate_accepted') {
                $format['search'] = ['{user_name}', '{job_title}', '{company_name}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['job_title'] ?? '',
                        $data['company_name'] ?? ''
                    ];
                }
            }

            if ($type === 'candidate_rejected') {
                $format['search'] = ['{user_name}', '{job_title}', '{company_name}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['job_title'] ?? '',
                        $data['company_name'] ?? ''
                    ];
                }
            }

            if ($type === 'interview_invitation') {
                $format['search'] = ['{user_name}', '{job_title}', '{company_name}', '{interview_date}', '{interview_time}', '{interview_location}', '{interview_type}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['job_title'] ?? '',
                        $data['company_name'] ?? '',
                        $data['interview_date'] ?? '',
                        $data['interview_time'] ?? '',
                        $data['interview_location'] ?? '',
                        $data['interview_type'] ?? ''
                    ];
                }
            }

            if ($type === 'reset_password') {
                $format['search'] = ['{user_name}', '{reset_link}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['reset_link'] ?? ''
                    ];
                }
            }

            if ($type === 'email_verification') {
                $format['search'] = ['{user_name}', '{verification_link}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['verification_link'] ?? ''
                    ];
                }
            }

            if ($type === 'new_job_notification') {
                $format['search'] = ['{user_name}', '{job_title}', '{company_name}', '{job_location}', '{job_salary}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['job_title'] ?? '',
                        $data['company_name'] ?? '',
                        $data['job_location'] ?? '',
                        $data['job_salary'] ?? ''
                    ];
                }
            }

            if ($type === 'flyer_approved') {
                $format['search'] = ['{user_name}', '{job_title}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['job_title'] ?? ''
                    ];
                }
            }

            if ($type === 'flyer_rejected') {
                $format['search'] = ['{user_name}', '{job_title}', '{rejection_reason}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['job_title'] ?? '',
                        $data['rejection_reason'] ?? ''
                    ];
                }
            }

            if ($type === 'new_message') {
                $format['search'] = ['{user_name}', '{sender_name}'];

                if ($data !== null) {
                    $format['replace'] = [
                        $data['user_name'] ?? '',
                        $data['sender_name'] ?? ''
                    ];
                }
            }

            return $format;
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }
}
